"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalWebviewPanel = void 0;
const vscode = __importStar(require("vscode"));
class TerminalWebviewPanel {
    constructor(_extensionUri, _terminalManager) {
        this._extensionUri = _extensionUri;
        this._terminalManager = _terminalManager;
        this._disposables = [];
        // Create webview panel
        this._panel = vscode.window.createWebviewPanel(TerminalWebviewPanel.viewType, 'Custom Terminal', vscode.ViewColumn.One, {
            enableScripts: true,
            localResourceRoots: [this._extensionUri],
            retainContextWhenHidden: true
        });
        // Set HTML content
        this._panel.webview.html = this._getHtmlForWebview(this._panel.webview);
        // Handle messages from webview
        this._panel.webview.onDidReceiveMessage(message => {
            console.log('Panel received message:', message);
            this.handleWebviewMessage(message);
        }, null, this._disposables);
        // Handle panel disposal
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        // Setup terminal manager events
        this.setupTerminalManagerEvents();
        // Send initial state
        this.sendTerminalList();
    }
    /**
     * Show specific terminal
     */
    showTerminal(terminalId) {
        console.log('Panel showTerminal called with ID:', terminalId);
        this._currentTerminalId = terminalId;
        this._panel.webview.postMessage({
            type: 'showTerminal',
            terminalId: terminalId
        });
    }
    /**
     * Reveal the panel
     */
    reveal() {
        this._panel.reveal();
    }
    /**
     * Handle disposal
     */
    dispose() {
        this._panel.dispose();
        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }
    /**
     * Get onDidDispose event
     */
    onDidDispose(listener) {
        return this._panel.onDidDispose(listener);
    }
    /**
     * Handle messages from webview
     */
    handleWebviewMessage(message) {
        switch (message.type) {
            case 'ready':
                this.sendTerminalList();
                break;
            case 'selectTerminal':
                this.selectTerminal(message.terminalId);
                break;
            case 'executeCommand':
                this.executeCommand(message.terminalId, message.command);
                break;
            case 'resizeTerminal':
                this.resizeTerminal(message.terminalId, message.cols, message.rows);
                break;
            case 'clearTerminal':
                this.clearTerminal(message.terminalId);
                break;
            case 'closeTerminal':
                this.closeTerminal(message.terminalId);
                break;
            case 'createTerminal':
                this.createTerminal();
                break;
        }
    }
    /**
     * Setup terminal manager event handlers
     */
    setupTerminalManagerEvents() {
        this._terminalManager.on('terminalCreated', (terminal) => {
            this.sendTerminalList();
            this.setupTerminalEvents(terminal);
        });
        this._terminalManager.on('terminalClosed', (terminalId) => {
            this.sendTerminalList();
            if (this._currentTerminalId === terminalId) {
                this._currentTerminalId = undefined;
            }
        });
        this._terminalManager.on('terminalData', (terminalId, data) => {
            this._panel.webview.postMessage({
                type: 'terminalData',
                terminalId: terminalId,
                data: data
            });
        });
    }
    /**
     * Setup events for a specific terminal
     */
    setupTerminalEvents(terminal) {
        terminal.on('statusChanged', (status) => {
            this._panel.webview.postMessage({
                type: 'terminalStatusChanged',
                terminalId: terminal.id,
                status: status
            });
        });
        terminal.on('commandExecuted', (execution) => {
            this._panel.webview.postMessage({
                type: 'commandExecuted',
                terminalId: terminal.id,
                execution: execution
            });
        });
    }
    /**
     * Send terminal list to webview
     */
    sendTerminalList() {
        const terminals = this._terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus()
        }));
        console.log('Panel sending terminal list:', terminals);
        this._panel.webview.postMessage({
            type: 'terminalList',
            terminals: terminals
        });
    }
    /**
     * Select terminal
     */
    selectTerminal(terminalId) {
        this._currentTerminalId = terminalId;
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            this._panel.webview.postMessage({
                type: 'terminalSelected',
                terminalId: terminalId,
                output: terminal.getOutput(),
                history: terminal.getCommandHistory()
            });
        }
    }
    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId, command) {
        try {
            await this._terminalManager.executeCommand(terminalId, command);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to execute command: ${error}`);
        }
    }
    /**
     * Resize terminal
     */
    resizeTerminal(terminalId, cols, rows) {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }
    /**
     * Clear terminal
     */
    clearTerminal(terminalId) {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }
    /**
     * Close terminal
     */
    async closeTerminal(terminalId) {
        try {
            await this._terminalManager.closeTerminal(terminalId);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to close terminal: ${error}`);
        }
    }
    /**
     * Create new terminal
     */
    async createTerminal() {
        try {
            const terminal = await this._terminalManager.createLocalTerminal();
            this.showTerminal(terminal.id);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    }
    /**
     * Get HTML for webview
     */
    _getHtmlForWebview(webview) {
        // Get URIs for resources
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.css'));
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>Custom Terminal</title>
            </head>
            <body>
                <div id="app">
                    <div id="terminal-tabs"></div>
                    <div id="terminal-container"></div>
                    <div id="terminal-controls">
                        <button id="create-terminal-btn">New Terminal</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
exports.TerminalWebviewPanel = TerminalWebviewPanel;
TerminalWebviewPanel.viewType = 'customTerminal.terminalPanel';
//# sourceMappingURL=TerminalWebviewPanel.js.map