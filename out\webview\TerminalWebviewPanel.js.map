{"version": 3, "file": "TerminalWebviewPanel.js", "sourceRoot": "", "sources": ["../../src/webview/TerminalWebviewPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,MAAa,oBAAoB;IAO7B,YACqB,aAAyB,EACzB,gBAAiC;QADjC,kBAAa,GAAb,aAAa,CAAY;QACzB,qBAAgB,GAAhB,gBAAgB,CAAiB;QAJ9C,iBAAY,GAAwB,EAAE,CAAC;QAM3C,uBAAuB;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,oBAAoB,CAAC,QAAQ,EAC7B,iBAAiB,EACjB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,+BAA+B;QAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CACpB,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,gCAAgC;QAChC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,UAAkB;QAClC,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;YAC5B,IAAI,EAAE,cAAc;YACpB,UAAU,EAAE,UAAU;SACzB,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,OAAO,EAAE,CAAC;aACxB;SACJ;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,QAAoB;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAY;QACrC,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,OAAO;gBACR,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxC,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;SACb;IACL,CAAC;IAED;;OAEG;IACK,0BAA0B;QAC9B,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,UAAkB,EAAE,EAAE;YAC9D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,kBAAkB,KAAK,UAAU,EAAE;gBACxC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;aACvC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE;YAC1E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,cAAc;gBACpB,UAAU,EAAE,UAAU;gBACtB,IAAI,EAAE,IAAI;aACb,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAkB;QAC1C,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,uBAAuB;gBAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,iBAAiB;gBACvB,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,SAAS,EAAE,SAAS;aACvB,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,gBAAgB;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvE,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;SAC/B,CAAC,CAAC,CAAC;QAEJ,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;YAC5B,IAAI,EAAE,cAAc;YACpB,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAkB;QACrC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE/D,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;gBAC5B,OAAO,EAAE,QAAQ,CAAC,iBAAiB,EAAE;aACxC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe;QAC5D,IAAI;YACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACnE;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,UAAkB;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,UAAkB;QAC1C,IAAI;YACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SACzD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;SACxE;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QACxB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;YACnE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAuB;QAC9C,yBAAyB;QACzB,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;QACvI,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;QACzI,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;QACxG,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;QAExG,OAAO;;;;;oGAKqF,OAAO,CAAC,SAAS,gCAAgC,OAAO,CAAC,SAAS;8BACxI,WAAW;8BACX,QAAQ;;;;;;;;;;;+BAWP,UAAU;+BACV,SAAS;;oBAEpB,CAAC;IACjB,CAAC;;AA9RL,oDA+RC;AA9R0B,6BAAQ,GAAG,8BAA8B,CAAC"}