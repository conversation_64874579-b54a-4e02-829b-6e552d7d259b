import * as vscode from 'vscode';
import { CustomTerminalProvider, CustomTerminalOptions } from '../terminal/CustomTerminalProvider';

/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
export class TerminalAPI {
    constructor(private terminalProvider: CustomTerminalProvider) {}

    /**
     * Create a new terminal
     */
    async createTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return await this.terminalProvider.createTerminal(options);
    }

    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(host: string, options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return await this.terminalProvider.createSSHTerminal(host, options);
    }

    /**
     * Create a PowerShell terminal
     */
    async createPowerShellTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return await this.terminalProvider.createPowerShellTerminal(options);
    }

    /**
     * Create a Command Prompt terminal
     */
    async createCmdTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return await this.terminalProvider.createCmdTerminal(options);
    }

    /**
     * Create a Bash terminal
     */
    async createBashTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return await this.terminalProvider.createBashTerminal(options);
    }

    /**
     * Get all terminals
     */
    getAllTerminals(): vscode.Terminal[] {
        return this.terminalProvider.getAllTerminals();
    }

    /**
     * Get terminal by ID
     */
    getTerminalById(id: string): vscode.Terminal | undefined {
        return this.terminalProvider.getTerminalById(id);
    }

    /**
     * Send text to terminal
     */
    sendText(terminalId: string, text: string, addNewLine: boolean = true): void {
        this.terminalProvider.sendText(terminalId, text, addNewLine);
    }

    /**
     * Show terminal
     */
    showTerminal(terminalId: string): void {
        this.terminalProvider.showTerminal(terminalId);
    }

    /**
     * Execute command in terminal
     */
    executeCommand(terminalId: string, command: string): void {
        this.terminalProvider.executeCommand(terminalId, command);
    }

    /**
     * Get terminal count
     */
    getTerminalCount(): number {
        return this.terminalProvider.getTerminalCount();
    }

    /**
     * Dispose terminal
     */
    disposeTerminal(terminalId: string): void {
        this.terminalProvider.disposeTerminal(terminalId);
    }
}