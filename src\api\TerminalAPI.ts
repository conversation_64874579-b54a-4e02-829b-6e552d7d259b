import { EventEmitter } from 'events';
import { TerminalManager, SSHConfig, TerminalConfig } from '../terminal/TerminalManager';
import { Terminal, TerminalStatus, CommandExecution } from '../terminal/Terminal';
import { TerminalSession } from '../terminal/TerminalSession';

export interface TerminalInfo {
    id: string;
    type: string;
    status: TerminalStatus;
    output: string;
    commandHistory: string[];
    executions: CommandExecution[];
}

export interface OutputSubscription {
    terminalId: string;
    callback: (data: string) => void;
    unsubscribe: () => void;
}

/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
export class TerminalAPI extends EventEmitter {
    private terminalManager: TerminalManager;
    private outputSubscriptions: Map<string, OutputSubscription[]> = new Map();

    constructor(terminalManager: TerminalManager) {
        super();
        this.terminalManager = terminalManager;
        this.setupEventForwarding();
    }

    /**
     * Create a new local terminal
     */
    async createLocalTerminal(config?: TerminalConfig): Promise<string> {
        const terminal = await this.terminalManager.createLocalTerminal(config);
        return terminal.id;
    }

    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(sshConfig: SSHConfig, terminalConfig?: TerminalConfig): Promise<string> {
        const terminal = await this.terminalManager.createSSHTerminal(sshConfig, terminalConfig);
        return terminal.id;
    }

    /**
     * Get terminal information
     */
    getTerminalInfo(terminalId: string): TerminalInfo | null {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (!terminal) {
            return null;
        }

        return {
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        };
    }

    /**
     * Get all terminals
     */
    getAllTerminals(): TerminalInfo[] {
        return this.terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        }));
    }

    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId: string, command: string): Promise<void> {
        await this.terminalManager.executeCommand(terminalId, command);
    }

    /**
     * Send input to terminal
     */
    async sendInput(terminalId: string, input: string): Promise<void> {
        await this.terminalManager.sendInput(terminalId, input);
    }

    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId: string): string {
        return this.terminalManager.getTerminalOutput(terminalId);
    }

    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId: string, callback: (data: string) => void): () => void {
        const unsubscribe = this.terminalManager.subscribeToOutput(terminalId, callback);
        
        // Track subscription
        const subscription: OutputSubscription = {
            terminalId,
            callback,
            unsubscribe
        };

        if (!this.outputSubscriptions.has(terminalId)) {
            this.outputSubscriptions.set(terminalId, []);
        }
        this.outputSubscriptions.get(terminalId)!.push(subscription);

        // Return unsubscribe function
        return () => {
            unsubscribe();
            this.removeSubscription(terminalId, subscription);
        };
    }

    /**
     * Subscribe to all terminal events
     */
    subscribeToTerminalEvents(callback: (event: string, ...args: any[]) => void): () => void {
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError',
            'sessionCreated',
            'sessionDeleted'
        ];

        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                callback(event, ...args);
            });
        });

        return () => {
            events.forEach(event => {
                this.terminalManager.removeAllListeners(event);
            });
        };
    }

    /**
     * Close terminal
     */
    async closeTerminal(terminalId: string): Promise<void> {
        await this.terminalManager.closeTerminal(terminalId);
        this.cleanupSubscriptions(terminalId);
    }

    /**
     * Resize terminal
     */
    resizeTerminal(terminalId: string, cols: number, rows: number): void {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }

    /**
     * Clear terminal
     */
    clearTerminal(terminalId: string): void {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }

    /**
     * Get terminal status
     */
    getTerminalStatus(terminalId: string): TerminalStatus | null {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getStatus() : null;
    }

    /**
     * Check if terminal exists
     */
    terminalExists(terminalId: string): boolean {
        return this.terminalManager.getTerminal(terminalId) !== undefined;
    }

    /**
     * Get command history for terminal
     */
    getCommandHistory(terminalId: string): string[] {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getCommandHistory() : [];
    }

    /**
     * Get command executions for terminal
     */
    getCommandExecutions(terminalId: string): CommandExecution[] {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getExecutions() : [];
    }

    /**
     * Create a new session
     */
    createSession(name: string, terminalIds: string[]): string {
        const session = this.terminalManager.createSession(name, terminalIds);
        return session.id;
    }

    /**
     * Get session information
     */
    getSession(sessionId: string): TerminalSession | null {
        return this.terminalManager.getSession(sessionId) || null;
    }

    /**
     * Get all sessions
     */
    getAllSessions(): TerminalSession[] {
        return this.terminalManager.getAllSessions();
    }

    /**
     * Delete session
     */
    deleteSession(sessionId: string): void {
        this.terminalManager.deleteSession(sessionId);
    }

    /**
     * Execute command and wait for completion
     */
    async executeCommandAndWait(terminalId: string, command: string, timeout: number = 30000): Promise<string> {
        return new Promise((resolve, reject) => {
            const terminal = this.terminalManager.getTerminal(terminalId);
            if (!terminal) {
                reject(new Error(`Terminal ${terminalId} not found`));
                return;
            }

            let output = '';
            let timeoutHandle: NodeJS.Timeout;

            const dataHandler = (data: string) => {
                output += data;
            };

            const commandHandler = (execution: CommandExecution) => {
                if (execution.command === command.trim() && execution.endTime) {
                    terminal.off('data', dataHandler);
                    terminal.off('commandExecuted', commandHandler);
                    clearTimeout(timeoutHandle);
                    resolve(execution.output);
                }
            };

            terminal.on('data', dataHandler);
            terminal.on('commandExecuted', commandHandler);

            timeoutHandle = setTimeout(() => {
                terminal.off('data', dataHandler);
                terminal.off('commandExecuted', commandHandler);
                reject(new Error(`Command execution timeout after ${timeout}ms`));
            }, timeout);

            this.executeCommand(terminalId, command).catch(reject);
        });
    }

    /**
     * Setup event forwarding from terminal manager
     */
    private setupEventForwarding(): void {
        // Forward all terminal manager events
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError',
            'sessionCreated',
            'sessionDeleted'
        ];

        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                this.emit(event, ...args);
            });
        });
    }

    /**
     * Remove subscription
     */
    private removeSubscription(terminalId: string, subscription: OutputSubscription): void {
        const subscriptions = this.outputSubscriptions.get(terminalId);
        if (subscriptions) {
            const index = subscriptions.indexOf(subscription);
            if (index > -1) {
                subscriptions.splice(index, 1);
            }
            if (subscriptions.length === 0) {
                this.outputSubscriptions.delete(terminalId);
            }
        }
    }

    /**
     * Cleanup subscriptions for terminal
     */
    private cleanupSubscriptions(terminalId: string): void {
        const subscriptions = this.outputSubscriptions.get(terminalId);
        if (subscriptions) {
            subscriptions.forEach(sub => sub.unsubscribe());
            this.outputSubscriptions.delete(terminalId);
        }
    }
}
