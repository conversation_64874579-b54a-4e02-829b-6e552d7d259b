import { EventEmitter } from 'events';
import { TerminalManager, TerminalConfig, SSHConfig } from '../terminal/TerminalManager';
import { Terminal, TerminalStatus, CommandExecution } from '../terminal/Terminal';

export interface TerminalInfo {
    id: string;
    type: string;
    status: TerminalStatus;
    output: string;
    commandHistory: string[];
    executions: CommandExecution[];
}

/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
export class TerminalAPI extends EventEmitter {
    constructor(private terminalManager: TerminalManager) {
        super();
        this.setupEventForwarding();
    }

    /**
     * Create a new local terminal
     */
    async createLocalTerminal(config?: TerminalConfig): Promise<string> {
        const terminal = await this.terminalManager.createLocalTerminal(config);
        return terminal.id;
    }

    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(sshConfig: SSHConfig, terminalConfig?: TerminalConfig): Promise<string> {
        const terminal = await this.terminalManager.createSSHTerminal(sshConfig, terminalConfig);
        return terminal.id;
    }

    /**
     * Get terminal information
     */
    getTerminalInfo(terminalId: string): TerminalInfo | null {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (!terminal) {
            return null;
        }

        return {
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        };
    }

    /**
     * Get all terminals
     */
    getAllTerminals(): TerminalInfo[] {
        return this.terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        }));
    }

    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId: string, command: string): Promise<void> {
        await this.terminalManager.executeCommand(terminalId, command);
    }

    /**
     * Send input to terminal
     */
    async sendInput(terminalId: string, input: string): Promise<void> {
        await this.terminalManager.sendInput(terminalId, input);
    }

    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId: string): string {
        return this.terminalManager.getTerminalOutput(terminalId);
    }

    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId: string, callback: (data: string) => void): () => void {
        return this.terminalManager.subscribeToOutput(terminalId, callback);
    }

    /**
     * Close terminal
     */
    async closeTerminal(terminalId: string): Promise<void> {
        await this.terminalManager.closeTerminal(terminalId);
    }

    /**
     * Resize terminal
     */
    resizeTerminal(terminalId: string, cols: number, rows: number): void {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }

    /**
     * Clear terminal
     */
    clearTerminal(terminalId: string): void {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }

    /**
     * Setup event forwarding from terminal manager
     */
    private setupEventForwarding(): void {
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError'
        ];

        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                this.emit(event, ...args);
            });
        });
    }
}