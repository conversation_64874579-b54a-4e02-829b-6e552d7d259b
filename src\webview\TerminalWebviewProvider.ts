import * as vscode from 'vscode';
import { TerminalManager } from '../terminal/TerminalManager';
import { Terminal } from '../terminal/Terminal';

export class TerminalWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'customTerminal.terminalView';

    private _view?: vscode.WebviewView;
    private _currentTerminalId?: string;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _terminalManager: TerminalManager
    ) {
        this.setupTerminalManagerEvents();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('Resolving webview view...');
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };

        console.log('Setting webview HTML...');
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(
            message => {
                console.log('Received message from webview:', message);
                this.handleWebviewMessage(message);
            },
            undefined,
            []
        );

        // Send initial state
        console.log('Sending initial terminal list...');
        this.sendTerminalList();
        console.log('Webview view resolved');
    }

    /**
     * Show specific terminal
     */
    public showTerminal(terminalId: string): void {
        console.log('showTerminal called with ID:', terminalId);
        this._currentTerminalId = terminalId;
        if (this._view) {
            console.log('Sending showTerminal message to webview');
            this._view.webview.postMessage({
                type: 'showTerminal',
                terminalId: terminalId
            });
        } else {
            console.log('Warning: _view is not available');
        }
    }

    /**
     * Handle messages from webview
     */
    private handleWebviewMessage(message: any): void {
        switch (message.type) {
            case 'ready':
                this.sendTerminalList();
                break;

            case 'selectTerminal':
                this.selectTerminal(message.terminalId);
                break;

            case 'sendInput':
                this.sendInputToTerminal(message.terminalId, message.input);
                break;

            case 'executeCommand':
                this.executeCommand(message.terminalId, message.command);
                break;

            case 'resizeTerminal':
                this.resizeTerminal(message.terminalId, message.cols, message.rows);
                break;

            case 'clearTerminal':
                this.clearTerminal(message.terminalId);
                break;

            case 'closeTerminal':
                this.closeTerminal(message.terminalId);
                break;

            case 'createLocalTerminal':
                this.createLocalTerminal();
                break;
        }
    }

    /**
     * Setup terminal manager event handlers
     */
    private setupTerminalManagerEvents(): void {
        this._terminalManager.on('terminalCreated', (terminal: Terminal) => {
            this.sendTerminalList();
            this.setupTerminalEvents(terminal);
        });

        this._terminalManager.on('terminalClosed', (terminalId: string) => {
            this.sendTerminalList();
            if (this._currentTerminalId === terminalId) {
                this._currentTerminalId = undefined;
            }
        });

        this._terminalManager.on('terminalData', (terminalId: string, data: string) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'terminalData',
                    terminalId: terminalId,
                    data: data
                });
            }
        });
    }

    /**
     * Setup events for a specific terminal
     */
    private setupTerminalEvents(terminal: Terminal): void {
        terminal.on('statusChanged', (status) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'terminalStatusChanged',
                    terminalId: terminal.id,
                    status: status
                });
            }
        });

        terminal.on('commandExecuted', (execution) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'commandExecuted',
                    terminalId: terminal.id,
                    execution: execution
                });
            }
        });
    }

    /**
     * Send terminal list to webview
     */
    private sendTerminalList(): void {
        if (this._view) {
            const terminals = this._terminalManager.getAllTerminals().map(terminal => ({
                id: terminal.id,
                type: terminal.type,
                status: terminal.getStatus()
            }));

            console.log('Sending terminal list to webview:', terminals);
            this._view.webview.postMessage({
                type: 'terminalList',
                terminals: terminals
            });
        } else {
            console.log('Cannot send terminal list: _view is not available');
        }
    }

    /**
     * Select terminal
     */
    private selectTerminal(terminalId: string): void {
        this._currentTerminalId = terminalId;
        const terminal = this._terminalManager.getTerminal(terminalId);

        if (terminal && this._view) {
            this._view.webview.postMessage({
                type: 'terminalSelected',
                terminalId: terminalId,
                output: terminal.getOutput(),
                history: terminal.getCommandHistory()
            });
        }
    }

    /**
     * Send input to terminal
     */
    private async sendInputToTerminal(terminalId: string, input: string): Promise<void> {
        try {
            await this._terminalManager.sendInput(terminalId, input);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to send input: ${error}`);
        }
    }

    /**
     * Execute command in terminal
     */
    private async executeCommand(terminalId: string, command: string): Promise<void> {
        try {
            await this._terminalManager.executeCommand(terminalId, command);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute command: ${error}`);
        }
    }

    /**
     * Resize terminal
     */
    private resizeTerminal(terminalId: string, cols: number, rows: number): void {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }

    /**
     * Clear terminal
     */
    private clearTerminal(terminalId: string): void {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }

    /**
     * Close terminal
     */
    private async closeTerminal(terminalId: string): Promise<void> {
        try {
            await this._terminalManager.closeTerminal(terminalId);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to close terminal: ${error}`);
        }
    }

    /**
     * Create local terminal
     */
    private async createLocalTerminal(): Promise<void> {
        try {
            const terminal = await this._terminalManager.createLocalTerminal();
            this.showTerminal(terminal.id);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    }



    /**
     * Get HTML for webview
     */
    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get URIs for resources
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>Custom Terminal</title>
            </head>
            <body>
                <div id="app">
                    <div id="terminal-tabs"></div>
                    <div id="terminal-container"></div>
                    <div id="terminal-controls">
                        <button id="create-local-btn">New Local Terminal</button>
                        <button id="create-ssh-btn">New SSH Terminal</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
