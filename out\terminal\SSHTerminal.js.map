{"version": 3, "file": "SSHTerminal.js", "sourceRoot": "", "sources": ["../../src/terminal/SSHTerminal.ts"], "names": [], "mappings": ";;;AAEA,yCAAqF;AAGrF,MAAa,WAAY,SAAQ,mBAAQ;IAMrC,YAAY,EAAU,EAAE,SAAoB,EAAE,UAA2B,EAAE;QACvE,KAAK,CAAC,EAAE,EAAE,uBAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QALjC,sBAAiB,GAAW,CAAC,CAAC;QAC9B,yBAAoB,GAAW,CAAC,CAAC;QACjC,mBAAc,GAAW,IAAI,CAAC,CAAC,YAAY;QAI/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;SACtC;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QAC/B,8BAA8B;QAC9B,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,YAAY,IAAI,iBAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,CAAC;QACrE,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,YAAY,IAAI,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC;YAC/D,IAAI,CAAC,YAAY,IAAI,yCAAyC,CAAC;YAC/D,IAAI,CAAC,YAAY,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC;YAEvD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3B,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,yBAAc,CAAC,SAAS,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACpD;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE,EAAE;YAC1F,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5C;QAED,mBAAmB;QACnB,MAAM,UAAU,GAAG,GAAG,OAAO,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,IAAI,UAAU,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAE9B,8BAA8B;QAC9B,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAEzC,IAAI,GAAG,KAAK,MAAM,EAAE;gBAChB,MAAM,GAAG,4IAA4I,CAAC;aACzJ;iBAAM,IAAI,GAAG,KAAK,QAAQ,EAAE;gBACzB,MAAM,GAAG,UAAU,CAAC;aACvB;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE;gBAC3B,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC;aACzC;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;gBACtB,MAAM,GAAG,gBAAgB,CAAC;aAC7B;iBAAM,IAAI,GAAG,KAAK,IAAI,EAAE;gBACrB,MAAM,GAAG,oCAAoC,CAAC;aACjD;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;gBACvB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO;aACV;iBAAM,IAAI,GAAG,KAAK,EAAE,EAAE;gBACnB,MAAM,GAAG,SAAS,OAAO,yBAAyB,CAAC;aACtD;YAED,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aAC7B;YAED,aAAa;YACb,MAAM,MAAM,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC;YACjD,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC9B,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,IAAI,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC;QACjF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;QAC/E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAA6B;QACzC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,WAAW;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,yBAAc,CAAC,SAAS,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;CACJ;AAzID,kCAyIC"}