{"name": "@xterm/addon-fit", "version": "0.10.0", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-fit.js", "types": "typings/addon-fit.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-fit", "license": "MIT", "keywords": ["terminal", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package"}, "peerDependencies": {"@xterm/xterm": "^5.0.0"}}