{"version": 3, "file": "TerminalWebviewProvider.js", "sourceRoot": "", "sources": ["../../src/webview/TerminalWebviewProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,MAAa,uBAAuB;IAMhC,YACqB,aAAyB,EACzB,gBAAiC;QADjC,kBAAa,GAAb,aAAa,CAAY;QACzB,qBAAgB,GAAhB,gBAAgB,CAAiB;QAElD,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEM,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,IAAI,CAAC,aAAa;aACrB;SACJ,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,+BAA+B;QAC/B,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,EACD,SAAS,EACT,EAAE,CACL,CAAC;QAEF,qBAAqB;QACrB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,UAAkB;QAClC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,cAAc;gBACpB,UAAU,EAAE,UAAU;aACzB,CAAC,CAAC;SACN;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;SAClD;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAY;QACrC,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,OAAO;gBACR,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxC,MAAM;YAEV,KAAK,WAAW;gBACZ,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5D,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM;YAEV,KAAK,gBAAgB;gBACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM;YAEV,KAAK,qBAAqB;gBACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;SACb;IACL,CAAC;IAED;;OAEG;IACK,0BAA0B;QAC9B,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,UAAkB,EAAE,EAAE;YAC9D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,kBAAkB,KAAK,UAAU,EAAE;gBACxC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;aACvC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE;YAC1E,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,IAAI,EAAE,cAAc;oBACpB,UAAU,EAAE,UAAU;oBACtB,IAAI,EAAE,IAAI;iBACb,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAkB;QAC1C,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,IAAI,EAAE,uBAAuB;oBAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,MAAM,EAAE,MAAM;iBACjB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE;YACzC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,IAAI,EAAE,iBAAiB;oBACvB,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,SAAS,EAAE,SAAS;iBACvB,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,gBAAgB;QACpB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACvE,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;aAC/B,CAAC,CAAC,CAAC;YAEJ,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,SAAS;aACvB,CAAC,CAAC;SACN;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;SACpE;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAkB;QACrC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE/D,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;gBAC5B,OAAO,EAAE,QAAQ,CAAC,iBAAiB,EAAE;aACxC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB,EAAE,KAAa;QAC/D,IAAI;YACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC5D;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;SACpE;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe;QAC5D,IAAI;YACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACnE;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,UAAkB;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,UAAkB;QAC1C,IAAI;YACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SACzD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;SACxE;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC7B,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;YACnE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC;IAID;;OAEG;IACK,kBAAkB,CAAC,OAAuB;QAC9C,yBAAyB;QACzB,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACpG,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;QAEpG,OAAO;;;;;8BAKe,QAAQ;;;;;;;;;;;;+BAYP,SAAS;;oBAEpB,CAAC;IACjB,CAAC;;AAhSL,0DAiSC;AAhS0B,gCAAQ,GAAG,6BAA6B,CAAC"}