/* VSCode Terminal-like Styles */

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-terminal-background);
    color: var(--vscode-terminal-foreground);
    height: 100vh;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-terminal-background);
}

/* Terminal Tabs - VSCode Style */
#terminal-tabs {
    display: flex;
    background-color: var(--vscode-tab-inactiveBackground);
    border-bottom: 1px solid var(--vscode-tab-border);
    min-height: 35px;
    overflow-x: auto;
    flex-shrink: 0;
}

.terminal-tab {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--vscode-tab-inactiveBackground);
    color: var(--vscode-tab-inactiveForeground);
    border-right: 1px solid var(--vscode-tab-border);
    cursor: pointer;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    user-select: none;
    font-size: 13px;
}

.terminal-tab:hover {
    background-color: var(--vscode-tab-hoverBackground);
    color: var(--vscode-tab-hoverForeground);
}

.terminal-tab.active {
    background-color: var(--vscode-tab-activeBackground);
    color: var(--vscode-tab-activeForeground);
    border-bottom: 2px solid var(--vscode-tab-activeBorder);
}

.terminal-tab span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.close-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 16px;
    margin-left: 8px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    opacity: 0.7;
}

.close-btn:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    opacity: 1;
}

/* Terminal Container */
#terminal-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: var(--vscode-terminal-background);
}

/* XTerm Terminal Wrapper */
.xterm-terminal-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

/* Override xterm.js styles to match VSCode */
.xterm {
    background-color: var(--vscode-terminal-background) !important;
    color: var(--vscode-terminal-foreground) !important;
    font-family: var(--vscode-terminal-fontFamily, 'Consolas', 'Courier New', monospace) !important;
    font-size: var(--vscode-terminal-fontSize, 14px) !important;
    line-height: var(--vscode-terminal-lineHeight, 1.2) !important;
}

.xterm .xterm-viewport {
    background-color: var(--vscode-terminal-background) !important;
}

.xterm .xterm-screen {
    background-color: var(--vscode-terminal-background) !important;
}

.xterm .xterm-cursor {
    background-color: var(--vscode-terminalCursor-foreground) !important;
    color: var(--vscode-terminalCursor-background) !important;
}

.xterm .xterm-selection {
    background-color: var(--vscode-terminal-selectionBackground) !important;
}

/* Terminal Controls */
#terminal-controls {
    display: flex;
    gap: 8px;
    padding: 8px;
    background-color: var(--vscode-panel-background);
    border-top: 1px solid var(--vscode-panel-border);
    flex-shrink: 0;
}

.vscode-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border, transparent);
    padding: 6px 12px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    font-family: inherit;
    outline: none;
}

.vscode-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.vscode-button:active {
    background-color: var(--vscode-button-activeBackground);
}

.vscode-button:focus {
    outline: 1px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--vscode-descriptionForeground);
    text-align: center;
    padding: 20px;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: normal;
    color: var(--vscode-foreground);
}

.empty-state p {
    margin: 0 0 20px 0;
    font-size: 14px;
    line-height: 1.4;
}

/* Scrollbar Styling - VSCode Style */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-connected {
    background-color: var(--vscode-terminal-ansiGreen);
}

.status-connecting {
    background-color: var(--vscode-terminal-ansiYellow);
    animation: pulse 1.5s infinite;
}

.status-disconnected {
    background-color: var(--vscode-terminal-ansiRed);
}

.status-error {
    background-color: var(--vscode-terminal-ansiMagenta);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Focus Styles */
.terminal-tab:focus,
.vscode-button:focus {
    outline: 1px solid var(--vscode-focusBorder);
    outline-offset: -1px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .terminal-tab {
        border: 1px solid var(--vscode-contrastBorder);
    }
    
    .terminal-tab.active {
        border-width: 2px;
    }
    
    .vscode-button {
        border: 1px solid var(--vscode-contrastBorder);
    }
    
    .xterm {
        border: 1px solid var(--vscode-contrastBorder);
    }
}

/* Dark/Light Theme Adaptations */
body[data-vscode-theme-kind="vscode-dark"] .xterm {
    background-color: #1e1e1e !important;
    color: #cccccc !important;
}

body[data-vscode-theme-kind="vscode-light"] .xterm {
    background-color: #ffffff !important;
    color: #333333 !important;
}

body[data-vscode-theme-kind="vscode-high-contrast"] .xterm {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Responsive Design */
@media (max-width: 600px) {
    .terminal-tab {
        min-width: 100px;
        max-width: 150px;
        font-size: 12px;
    }
    
    #terminal-controls {
        flex-wrap: wrap;
    }
    
    .vscode-button {
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid var(--vscode-progressBar-background);
    border-radius: 50%;
    border-top-color: var(--vscode-progressBar-foreground);
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Terminal specific overrides */
.xterm-helper-textarea {
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
}

/* Ensure proper sizing */
.xterm-terminal-wrapper .xterm {
    width: 100% !important;
    height: 100% !important;
}
