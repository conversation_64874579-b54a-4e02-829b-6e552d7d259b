// @ts-check

(function() {
    'use strict';

    // Get VS Code API
    const vscode = acquireVsCodeApi();

    // Terminal instances
    const terminals = new Map();
    let currentTerminalId = null;

    // DOM elements
    let terminalContainer;
    let terminalTabs;
    let createLocalBtn;
    let createSSHBtn;

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM loaded, initializing...');
        initializeDOM();
        setupEventListeners();

        // Notify extension that webview is ready
        console.log('Sending ready message to extension');
        vscode.postMessage({ type: 'ready' });
    });

    function initializeDOM() {
        console.log('Initializing DOM elements...');
        terminalContainer = document.getElementById('terminal-container');
        terminalTabs = document.getElementById('terminal-tabs');
        createLocalBtn = document.getElementById('create-local-btn');
        createSSHBtn = document.getElementById('create-ssh-btn');

        console.log('DOM elements found:', {
            terminalContainer: !!terminalContainer,
            terminalTabs: !!terminalTabs,
            createLocalBtn: !!createLocalBtn,
            createSSHBtn: !!createSSHBtn
        });
    }

    function setupEventListeners() {
        // Create terminal buttons
        createLocalBtn.addEventListener('click', () => {
            vscode.postMessage({ type: 'createLocalTerminal' });
        });

        createSSHBtn.addEventListener('click', () => {
            createSSHTerminal();
        });

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            handleMessage(message);
        });
    }

    function handleMessage(message) {
        console.log('Received message:', message);
        switch (message.type) {
            case 'terminalList':
                console.log('Updating terminal list:', message.terminals);
                updateTerminalList(message.terminals);
                break;

            case 'showTerminal':
                console.log('Showing terminal:', message.terminalId);
                showTerminal(message.terminalId);
                break;

            case 'terminalSelected':
                console.log('Terminal selected:', message.terminalId);
                selectTerminal(message.terminalId, message.output, message.history);
                break;

            case 'terminalData':
                console.log('Terminal data received for:', message.terminalId);
                handleTerminalData(message.terminalId, message.data);
                break;

            case 'terminalStatusChanged':
                console.log('Terminal status changed:', message.terminalId, message.status);
                updateTerminalStatus(message.terminalId, message.status);
                break;

            case 'commandExecuted':
                console.log('Command executed:', message.terminalId);
                handleCommandExecuted(message.terminalId, message.execution);
                break;
        }
    }

    function updateTerminalList(terminalList) {
        console.log('updateTerminalList called with:', terminalList);
        // Clear existing tabs
        terminalTabs.innerHTML = '';

        if (terminalList.length === 0) {
            // Show empty state
            showEmptyState();
        } else {
            hideEmptyState();
            terminalList.forEach(terminal => {
                createTerminalTab(terminal);
            });
        }
    }

    function showEmptyState() {
        console.log('Showing empty state');
        terminalContainer.innerHTML = `
            <div class="empty-state">
                <h3>No terminals created yet</h3>
                <p>Click "New Local Terminal" or "New SSH Terminal" to get started</p>
            </div>
        `;
    }

    function hideEmptyState() {
        console.log('Hiding empty state');
        const emptyState = terminalContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }

    function createTerminalTab(terminal) {
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminal.id;

        const title = document.createElement('span');
        title.textContent = `${terminal.type} (${terminal.status})`;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-btn';
        closeBtn.textContent = '×';
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            closeTerminal(terminal.id);
        });

        tab.appendChild(title);
        tab.appendChild(closeBtn);

        tab.addEventListener('click', () => {
            selectTerminalTab(terminal.id);
        });

        terminalTabs.appendChild(tab);
    }

    function selectTerminalTab(terminalId) {
        // Update tab selection
        document.querySelectorAll('.terminal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const selectedTab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Request terminal selection
        vscode.postMessage({
            type: 'selectTerminal',
            terminalId: terminalId
        });
    }

    function showTerminal(terminalId) {
        selectTerminalTab(terminalId);
    }

    function selectTerminal(terminalId, output, history) {
        currentTerminalId = terminalId;

        // Create or get terminal instance
        if (!terminals.has(terminalId)) {
            createTerminalInstance(terminalId);
        }

        const terminal = terminals.get(terminalId);

        // Clear and write output
        terminal.clear();
        if (output) {
            terminal.write(output);
        }

        // Show terminal
        showTerminalInstance(terminalId);
    }

    function createTerminalInstance(terminalId) {
        // Create a simple terminal div for now
        // Will be replaced with actual xterm.js integration later
        const terminalDiv = document.createElement('div');
        terminalDiv.className = 'simple-terminal';
        terminalDiv.style.cssText = `
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            height: 100%;
            overflow-y: auto;
            white-space: pre-wrap;
        `;

        // Create input area
        const inputContainer = document.createElement('div');
        inputContainer.style.cssText = `
            display: flex;
            background: #2d2d30;
            border-top: 1px solid #3e3e42;
            padding: 5px;
        `;

        const inputField = document.createElement('input');
        inputField.type = 'text';
        inputField.placeholder = 'Type command and press Enter...';
        inputField.style.cssText = `
            flex: 1;
            background: #3c3c3c;
            color: #cccccc;
            border: 1px solid #464647;
            padding: 5px 10px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
        `;

        inputContainer.appendChild(inputField);

        const terminal = {
            element: terminalDiv,
            inputContainer: inputContainer,
            write: function(data) {
                terminalDiv.textContent += data;
                terminalDiv.scrollTop = terminalDiv.scrollHeight;
            },
            clear: function() {
                terminalDiv.textContent = '';
            },
            open: function(container) {
                const wrapper = document.createElement('div');
                wrapper.style.cssText = 'display: flex; flex-direction: column; height: 100%;';

                const terminalArea = document.createElement('div');
                terminalArea.style.cssText = 'flex: 1; overflow: hidden;';
                terminalArea.appendChild(terminalDiv);

                wrapper.appendChild(terminalArea);
                wrapper.appendChild(inputContainer);
                container.appendChild(wrapper);
            },
            dispose: function() {
                if (terminalDiv.parentNode) {
                    terminalDiv.parentNode.removeChild(terminalDiv);
                }
            },
            onData: function(callback) {
                inputField.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        const command = inputField.value;
                        if (command.trim()) {
                            // Send command
                            vscode.postMessage({
                                type: 'executeCommand',
                                terminalId: terminalId,
                                command: command
                            });
                            inputField.value = '';
                        }
                    }
                });
            },
            onResize: function(callback) {
                // Simple resize handling
                window.addEventListener('resize', () => {
                    callback({ cols: 80, rows: 24 });
                });
            }
        };

        // Handle terminal input
        terminal.onData(data => {
            if (currentTerminalId === terminalId) {
                vscode.postMessage({
                    type: 'sendInput',
                    terminalId: terminalId,
                    input: data
                });
            }
        });

        // Handle terminal resize
        terminal.onResize(({ cols, rows }) => {
            vscode.postMessage({
                type: 'resizeTerminal',
                terminalId: terminalId,
                cols: cols,
                rows: rows
            });
        });

        // Create terminal element
        const terminalElement = document.createElement('div');
        terminalElement.className = 'terminal-instance';
        terminalElement.dataset.terminalId = terminalId;
        terminalElement.style.display = 'none';

        terminal.open(terminalElement);
        terminalContainer.appendChild(terminalElement);

        terminals.set(terminalId, terminal);
    }

    function showTerminalInstance(terminalId) {
        // Hide all terminals
        document.querySelectorAll('.terminal-instance').forEach(el => {
            el.style.display = 'none';
        });

        // Show selected terminal
        const terminalElement = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (terminalElement) {
            terminalElement.style.display = 'block';

            // Fit terminal to container
            const terminal = terminals.get(terminalId);
            if (terminal && terminal.fit) {
                terminal.fit();
            }
        }
    }

    function handleTerminalData(terminalId, data) {
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.write(data);
        }
    }

    function updateTerminalStatus(terminalId, status) {
        const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (tab) {
            const title = tab.querySelector('span');
            if (title) {
                const terminalType = title.textContent.split(' ')[0];
                title.textContent = `${terminalType} (${status})`;
            }
        }
    }

    function handleCommandExecuted(terminalId, execution) {
        // Could add command execution indicators here
        console.log('Command executed:', execution);
    }

    function closeTerminal(terminalId) {
        vscode.postMessage({
            type: 'closeTerminal',
            terminalId: terminalId
        });

        // Clean up terminal instance
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.dispose();
            terminals.delete(terminalId);
        }

        // Remove terminal element
        const terminalElement = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (terminalElement) {
            terminalElement.remove();
        }

        // If this was the current terminal, clear current
        if (currentTerminalId === terminalId) {
            currentTerminalId = null;
        }
    }

    function createSSHTerminal() {
        const host = prompt('Enter SSH host (e.g., user@hostname):');
        if (!host) return;

        const port = prompt('Enter SSH port (default: 22):') || '22';

        const config = {
            host: host,
            port: parseInt(port)
        };

        vscode.postMessage({
            type: 'createSSHTerminal',
            config: config
        });
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        if (currentTerminalId) {
            const terminal = terminals.get(currentTerminalId);
            if (terminal && terminal.fit) {
                terminal.fit();
            }
        }
    });

})();
