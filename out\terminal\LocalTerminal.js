"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalTerminal = void 0;
// import * as pty from 'node-pty'; // Temporarily disabled
const os = __importStar(require("os"));
const Terminal_1 = require("./Terminal");
class LocalTerminal extends Terminal_1.Terminal {
    constructor(id, options = {}) {
        super(id, Terminal_1.TerminalType.LOCAL, options);
    }
    async initialize() {
        try {
            this.setStatus(Terminal_1.TerminalStatus.INITIALIZING);
            // For now, just simulate a terminal without actual process
            // This will be replaced with proper node-pty implementation later
            this.simulateTerminalProcess();
            this.setStatus(Terminal_1.TerminalStatus.CONNECTED);
            this.emit('initialized');
        }
        catch (error) {
            this.setStatus(Terminal_1.TerminalStatus.ERROR);
            this.emit('error', error);
            throw error;
        }
    }
    /**
     * Simulate terminal process for initial implementation
     */
    simulateTerminalProcess() {
        // Simulate a basic terminal prompt
        setTimeout(() => {
            const prompt = os.platform() === 'win32' ? 'C:\\> ' : '$ ';
            this.outputBuffer += `Custom Terminal v0.1.0\r\n${prompt}`;
            this.emit('data', `Custom Terminal v0.1.0\r\n${prompt}`);
        }, 100);
    }
    /**
     * Get default shell for the current platform
     */
    getDefaultShell() {
        const platform = os.platform();
        switch (platform) {
            case 'win32':
                // Try PowerShell first, then cmd
                const powershell = process.env.COMSPEC?.replace('cmd.exe', 'powershell.exe');
                if (powershell) {
                    return powershell;
                }
                return process.env.COMSPEC || 'cmd.exe';
            case 'darwin':
                return process.env.SHELL || '/bin/zsh';
            case 'linux':
            default:
                return process.env.SHELL || '/bin/bash';
        }
    }
    /**
     * Setup additional event handlers for local terminal
     */
    setupPtyEventHandlers() {
        super.setupPtyEventHandlers();
        if (!this.ptyProcess) {
            return;
        }
        // Handle process errors
        this.ptyProcess.onData((data) => {
            // Trim output buffer periodically to prevent memory issues
            if (this.outputBuffer.length > 1000000) { // 1MB
                this.trimOutputBuffer();
            }
        });
    }
    /**
     * Execute a command with enhanced local features
     */
    async executeCommand(command) {
        // Handle built-in commands
        if (this.handleBuiltinCommand(command)) {
            return;
        }
        // For now, simulate command execution
        await this.simulateCommandExecution(command);
    }
    /**
     * Simulate command execution for initial implementation
     */
    async simulateCommandExecution(command) {
        // Add to command history
        if (command.trim() && this.commandHistory[this.commandHistory.length - 1] !== command.trim()) {
            this.commandHistory.push(command.trim());
        }
        // Create command execution record
        const execution = {
            id: this.generateExecutionId(),
            command: command.trim(),
            startTime: new Date(),
            output: ''
        };
        this.currentExecution = execution;
        this.executions.push(execution);
        // Echo the command
        const echoOutput = `${command}\r\n`;
        this.outputBuffer += echoOutput;
        this.emit('data', echoOutput);
        // Simulate command processing
        setTimeout(() => {
            let output = '';
            const cmd = command.trim().toLowerCase();
            if (cmd === 'help') {
                output = 'Available commands:\r\n  help - Show this help\r\n  echo <text> - Echo text\r\n  date - Show current date\r\n  pwd - Show current directory\r\n  ls/dir - List directory contents\r\n  ssh <host> - Connect to SSH server\r\n  clear - Clear terminal\r\n  exit - Exit terminal\r\n';
            }
            else if (cmd.startsWith('echo ')) {
                output = cmd.substring(5) + '\r\n';
            }
            else if (cmd === 'date') {
                output = new Date().toString() + '\r\n';
            }
            else if (cmd === 'pwd') {
                output = process.cwd() + '\r\n';
            }
            else if (cmd === 'ls' || cmd === 'dir') {
                output = 'Simulated directory listing:\r\n  file1.txt\r\n  file2.js\r\n  folder1/\r\n';
            }
            else if (cmd.startsWith('ssh ')) {
                const host = cmd.substring(4).trim();
                if (host) {
                    output = `Connecting to ${host}...\r\nConnected to ${host}\r\nWelcome to ${host}\r\n`;
                    // Change prompt to show SSH connection
                    setTimeout(() => {
                        const sshPrompt = `user@${host}:~$ `;
                        this.outputBuffer += sshPrompt;
                        this.emit('data', sshPrompt);
                    }, 200);
                    return; // Don't add regular prompt
                }
                else {
                    output = 'Usage: ssh <hostname>\r\n';
                }
            }
            else if (cmd === 'whoami') {
                output = 'user\r\n';
            }
            else if (cmd === 'hostname') {
                output = 'localhost\r\n';
            }
            else if (cmd !== '') {
                output = `Command not found: ${command}\r\n`;
            }
            if (output) {
                this.outputBuffer += output;
                this.emit('data', output);
                execution.output += output;
            }
            // Add prompt
            const prompt = os.platform() === 'win32' ? 'C:\\> ' : '$ ';
            this.outputBuffer += prompt;
            this.emit('data', prompt);
            // Complete execution
            execution.endTime = new Date();
            execution.exitCode = 0;
            this.currentExecution = undefined;
            this.emit('commandExecuted', execution);
        }, 100);
    }
    generateExecutionId() {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Handle built-in commands
     */
    handleBuiltinCommand(command) {
        const trimmedCommand = command.trim().toLowerCase();
        switch (trimmedCommand) {
            case 'clear':
            case 'cls':
                this.clear();
                // Simulate clear screen and show prompt
                setTimeout(() => {
                    const prompt = os.platform() === 'win32' ? 'C:\\> ' : '$ ';
                    this.outputBuffer = prompt;
                    this.emit('data', '\x1b[2J\x1b[H' + prompt); // Clear screen ANSI sequence + prompt
                }, 10);
                return true;
            case 'exit':
            case 'quit':
                this.dispose();
                return true;
            default:
                return false;
        }
    }
    /**
     * Get current working directory
     */
    async getCurrentWorkingDirectory() {
        return new Promise((resolve, reject) => {
            if (!this.ptyProcess) {
                reject(new Error('Terminal not initialized'));
                return;
            }
            const platform = os.platform();
            let command;
            if (platform === 'win32') {
                command = 'cd\r';
            }
            else {
                command = 'pwd\r';
            }
            let output = '';
            const dataHandler = (data) => {
                output += data;
                if (data.includes('\n') || data.includes('\r')) {
                    this.ptyProcess?.off('data', dataHandler);
                    // Extract directory from output
                    const lines = output.split(/\r?\n/);
                    const dirLine = lines.find(line => line.trim() && !line.includes(command.trim()));
                    resolve(dirLine?.trim() || process.cwd());
                }
            };
            this.ptyProcess.on('data', dataHandler);
            this.ptyProcess.write(command);
            // Timeout after 5 seconds
            setTimeout(() => {
                this.ptyProcess?.off('data', dataHandler);
                resolve(process.cwd());
            }, 5000);
        });
    }
    /**
     * Change working directory
     */
    async changeDirectory(directory) {
        const platform = os.platform();
        let command;
        if (platform === 'win32') {
            command = `cd /d "${directory}"`;
        }
        else {
            command = `cd "${directory}"`;
        }
        await this.executeCommand(command);
    }
    /**
     * List directory contents
     */
    async listDirectory(directory) {
        const platform = os.platform();
        let command;
        if (platform === 'win32') {
            command = directory ? `dir "${directory}"` : 'dir';
        }
        else {
            command = directory ? `ls -la "${directory}"` : 'ls -la';
        }
        await this.executeCommand(command);
    }
}
exports.LocalTerminal = LocalTerminal;
//# sourceMappingURL=LocalTerminal.js.map