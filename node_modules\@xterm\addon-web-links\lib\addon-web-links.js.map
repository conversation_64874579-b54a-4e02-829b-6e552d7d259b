{"version": 3, "file": "addon-web-links.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAuB,cAAID,IAE3BD,EAAoB,cAAIC,GACzB,CATD,CASGK,MAAM,I,oCCkCT,SAASC,EAAMC,GACb,IACE,MAAMC,EAAM,IAAIC,IAAIF,GACdG,EAAaF,EAAIG,UAAYH,EAAII,SACnC,GAAGJ,EAAIK,aAAaL,EAAII,YAAYJ,EAAIG,YAAYH,EAAIM,OACxDN,EAAII,SACF,GAAGJ,EAAIK,aAAaL,EAAII,YAAYJ,EAAIM,OACxC,GAAGN,EAAIK,aAAaL,EAAIM,OAC9B,OAAOP,EAAUQ,oBAAoBC,WAAWN,EAAWK,oB,CAC3D,MAAOE,GACP,OAAO,C,CAEX,C,yFA1CA,wBAEE,WAAAC,CACmBC,EACAC,EACAC,EACAC,EAAiC,CAAC,GAHlC,KAAAH,UAAAA,EACA,KAAAC,OAAAA,EACA,KAAAC,SAAAA,EACA,KAAAC,SAAAA,CAGnB,CAEO,YAAAC,CAAaC,EAAWC,GAC7B,MAAMC,EAAQC,EAAaC,YAAYJ,EAAGK,KAAKT,OAAQS,KAAKV,UAAWU,KAAKR,UAC5EI,EAASI,KAAKC,cAAcJ,GAC9B,CAEQ,aAAAI,CAAcJ,GACpB,OAAOA,EAAMK,KAAIC,IACfA,EAAKC,MAAQJ,KAAKP,SAASW,MAC3BD,EAAKE,MAAQ,CAACC,EAAmBC,KAC/B,GAAIP,KAAKP,SAASY,MAAO,CACvB,MAAM,MAAEG,GAAUL,EAClBH,KAAKP,SAASY,MAAMC,EAAOC,EAAKC,E,GAG7BL,IAEX,GAiBF,MAAaL,EACJ,kBAAOC,CAAYJ,EAAWc,EAAeC,EAAoBC,GACtE,MAAMC,EAAM,IAAIC,OAAOJ,EAAMK,QAASL,EAAMM,OAAS,IAAM,MAEpDC,EAAOC,GAAkBnB,EAAaoB,wBAAwBvB,EAAI,EAAGe,GACtES,EAAOH,EAAMI,KAAK,IAExB,IAAIC,EACJ,MAAMC,EAAkB,GAExB,KAAOD,EAAQT,EAAIW,KAAKJ,IAAO,CAC7B,MAAMK,EAAOH,EAAM,GAGnB,IAAK5C,EAAM+C,GACT,SAKF,MAAOC,EAAQC,GAAU5B,EAAa6B,WAAWjB,EAAUO,EAAgB,EAAGI,EAAMO,QAC7EC,EAAMC,GAAQhC,EAAa6B,WAAWjB,EAAUe,EAAQC,EAAQF,EAAKO,QAE5E,IAAgB,IAAZN,IAA6B,IAAZC,IAA2B,IAAVG,IAAyB,IAAVC,EACnD,SAIF,MAAMtB,EAAQ,CACZwB,MAAO,CACLC,EAAGP,EAAS,EACZ/B,EAAG8B,EAAS,GAEdS,IAAK,CACHD,EAAGH,EACHnC,EAAGkC,EAAO,IAIdP,EAAOa,KAAK,CAAE3B,QAAOgB,OAAMb,Y,CAG7B,OAAOW,CACT,CAWQ,8BAAOJ,CAAwBkB,EAAmB1B,GACxD,IAAIS,EACAkB,EAASD,EACTE,EAAYF,EACZL,EAAS,EACTQ,EAAU,GACd,MAAMvB,EAAkB,GAExB,GAAKG,EAAOT,EAAS8B,OAAOC,OAAOC,QAAQN,GAAa,CACtD,MAAMO,EAAiBxB,EAAKyB,mBAAkB,GAG9C,GAAIzB,EAAK0B,WAAmC,MAAtBF,EAAe,GAAY,CAE/C,IADAZ,EAAS,GACDZ,EAAOT,EAAS8B,OAAOC,OAAOC,UAAUL,KAAYN,EAAS,OACnEQ,EAAUpB,EAAKyB,mBAAkB,GACjCb,GAAUQ,EAAQR,OAClBf,EAAMmB,KAAKI,GACNpB,EAAK0B,YAAuC,IAA1BN,EAAQO,QAAQ,QAIzC9B,EAAM+B,S,CAQR,IAJA/B,EAAMmB,KAAKQ,GAGXZ,EAAS,GACDZ,EAAOT,EAAS8B,OAAOC,OAAOC,UAAUJ,KAAenB,EAAK0B,WAAad,EAAS,OACxFQ,EAAUpB,EAAKyB,mBAAkB,GACjCb,GAAUQ,EAAQR,OAClBf,EAAMmB,KAAKI,IACmB,IAA1BA,EAAQO,QAAQ,Q,CAKxB,MAAO,CAAC9B,EAAOqB,EACjB,CAOQ,iBAAOV,CAAWjB,EAAoB0B,EAAmBY,EAAkBC,GACjF,MAAMC,EAAMxC,EAAS8B,OAAOC,OACtBU,EAAOD,EAAIE,cACjB,IAAIpB,EAAQgB,EACZ,KAAOC,GAAa,CAClB,MAAM9B,EAAO+B,EAAIR,QAAQN,GACzB,IAAKjB,EACH,MAAO,EAAE,GAAI,GAEf,IAAK,IAAIkC,EAAIrB,EAAOqB,EAAIlC,EAAKY,SAAUsB,EAAG,CACxClC,EAAKmC,QAAQD,EAAGF,GAChB,MAAMI,EAAQJ,EAAKK,WAEnB,GADcL,EAAKM,aAEjBR,GAAeM,EAAMxB,QAAU,EAO3BsB,IAAMlC,EAAKY,OAAS,GAAe,KAAVwB,GAAc,CACzC,MAAMpC,EAAO+B,EAAIR,QAAQN,EAAY,GACjCjB,GAAQA,EAAK0B,YACf1B,EAAKmC,QAAQ,EAAGH,GACQ,IAApBA,EAAKM,aACPR,GAAe,G,CAKvB,GAAIA,EAAc,EAChB,MAAO,CAACb,EAAWiB,E,CAGvBjB,IACAJ,EAAQ,C,CAEV,MAAO,CAACI,EAAWJ,EACrB,EA5IF,gB,GCxDI0B,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAazF,QAGrB,IAAIC,EAASqF,EAAyBE,GAAY,CAGjDxF,QAAS,CAAC,GAOX,OAHA2F,EAAoBH,GAAUvF,EAAQA,EAAOD,QAASuF,GAG/CtF,EAAOD,OACf,C,qGCfA,aAaM4F,EAAiB,4EAGvB,SAASC,EAAW3D,EAAmBC,GACrC,MAAM2D,EAAYC,OAAOC,OACzB,GAAIF,EAAW,CACb,IACEA,EAAUG,OAAS,I,CACnB,M,CAGFH,EAAUI,SAASC,KAAOhE,C,MAE1BiE,QAAQC,KAAK,sDAEjB,CAEA,sBAIE,WAAApF,CACUG,EAAqDyE,EACrDxE,EAAiC,CAAC,GADlC,KAAAD,SAAAA,EACA,KAAAC,SAAAA,CAEV,CAEO,QAAAkB,CAASD,GACdV,KAAKV,UAAYoB,EACjB,MAAMgE,EAAU1E,KAAKP,SACfgB,EAAQiE,EAAQC,UAAYX,EAClChE,KAAK4E,cAAgB5E,KAAKV,UAAUuF,qBAAqB,IAAI,EAAAC,gBAAgB9E,KAAKV,UAAWmB,EAAOT,KAAKR,SAAUkF,GACrH,CAEO,OAAAK,GACL/E,KAAK4E,eAAeG,SACtB,E", "sources": ["webpack://WebLinksAddon/webpack/universalModuleDefinition", "webpack://WebLinksAddon/./src/WebLinkProvider.ts", "webpack://WebLinksAddon/webpack/bootstrap", "webpack://WebLinksAddon/./src/WebLinksAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"WebLinksAddon\"] = factory();\n\telse\n\t\troot[\"WebLinksAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ILinkProvider, ILink, Terminal, IViewportRange, IBufferLine } from '@xterm/xterm';\n\nexport interface ILinkProviderOptions {\n  hover?(event: MouseEvent, text: string, location: IViewportRange): void;\n  leave?(event: MouseEvent, text: string): void;\n  urlRegex?: RegExp;\n}\n\nexport class WebLinkProvider implements ILinkProvider {\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _regex: RegExp,\n    private readonly _handler: (event: MouseEvent, uri: string) => void,\n    private readonly _options: ILinkProviderOptions = {}\n  ) {\n\n  }\n\n  public provideLinks(y: number, callback: (links: ILink[] | undefined) => void): void {\n    const links = LinkComputer.computeLink(y, this._regex, this._terminal, this._handler);\n    callback(this._addCallbacks(links));\n  }\n\n  private _addCallbacks(links: ILink[]): ILink[] {\n    return links.map(link => {\n      link.leave = this._options.leave;\n      link.hover = (event: MouseEvent, uri: string): void => {\n        if (this._options.hover) {\n          const { range } = link;\n          this._options.hover(event, uri, range);\n        }\n      };\n      return link;\n    });\n  }\n}\n\nfunction isUrl(urlString: string): boolean {\n  try {\n    const url = new URL(urlString);\n    const parsedBase = url.password && url.username\n      ? `${url.protocol}//${url.username}:${url.password}@${url.host}`\n      : url.username\n        ? `${url.protocol}//${url.username}@${url.host}`\n        : `${url.protocol}//${url.host}`;\n    return urlString.toLocaleLowerCase().startsWith(parsedBase.toLocaleLowerCase());\n  } catch (e) {\n    return false;\n  }\n}\n\nexport class LinkComputer {\n  public static computeLink(y: number, regex: RegExp, terminal: Terminal, activate: (event: MouseEvent, uri: string) => void): ILink[] {\n    const rex = new RegExp(regex.source, (regex.flags || '') + 'g');\n\n    const [lines, startLineIndex] = LinkComputer._getWindowedLineStrings(y - 1, terminal);\n    const line = lines.join('');\n\n    let match;\n    const result: ILink[] = [];\n\n    while (match = rex.exec(line)) {\n      const text = match[0];\n\n      // check via URL if the matched text would form a proper url\n      if (!isUrl(text)) {\n        continue;\n      }\n\n      // map string positions back to buffer positions\n      // values are 0-based right side excluding\n      const [startY, startX] = LinkComputer._mapStrIdx(terminal, startLineIndex, 0, match.index);\n      const [endY, endX] = LinkComputer._mapStrIdx(terminal, startY, startX, text.length);\n\n      if (startY === -1 || startX === -1 || endY === -1 || endX === -1) {\n        continue;\n      }\n\n      // range expects values 1-based right side including, thus +1 except for endX\n      const range = {\n        start: {\n          x: startX + 1,\n          y: startY + 1\n        },\n        end: {\n          x: endX,\n          y: endY + 1\n        }\n      };\n\n      result.push({ range, text, activate });\n    }\n\n    return result;\n  }\n\n  /**\n   * Get wrapped content lines for the current line index.\n   * The top/bottom line expansion stops at whitespaces or length > 2048.\n   * Returns an array with line strings and the top line index.\n   *\n   * NOTE: We pull line strings with trimRight=true on purpose to make sure\n   * to correctly match urls with early wrapped wide chars. This corrupts the string index\n   * for 1:1 backmapping to buffer positions, thus needs an additional correction in _mapStrIdx.\n   */\n  private static _getWindowedLineStrings(lineIndex: number, terminal: Terminal): [string[], number] {\n    let line: IBufferLine | undefined;\n    let topIdx = lineIndex;\n    let bottomIdx = lineIndex;\n    let length = 0;\n    let content = '';\n    const lines: string[] = [];\n\n    if ((line = terminal.buffer.active.getLine(lineIndex))) {\n      const currentContent = line.translateToString(true);\n\n      // expand top, stop on whitespaces or length > 2048\n      if (line.isWrapped && currentContent[0] !== ' ') {\n        length = 0;\n        while ((line = terminal.buffer.active.getLine(--topIdx)) && length < 2048) {\n          content = line.translateToString(true);\n          length += content.length;\n          lines.push(content);\n          if (!line.isWrapped || content.indexOf(' ') !== -1) {\n            break;\n          }\n        }\n        lines.reverse();\n      }\n\n      // append current line\n      lines.push(currentContent);\n\n      // expand bottom, stop on whitespaces or length > 2048\n      length = 0;\n      while ((line = terminal.buffer.active.getLine(++bottomIdx)) && line.isWrapped && length < 2048) {\n        content = line.translateToString(true);\n        length += content.length;\n        lines.push(content);\n        if (content.indexOf(' ') !== -1) {\n          break;\n        }\n      }\n    }\n    return [lines, topIdx];\n  }\n\n  /**\n   * Map a string index back to buffer positions.\n   * Returns buffer position as [lineIndex, columnIndex] 0-based,\n   * or [-1, -1] in case the lookup ran into a non-existing line.\n   */\n  private static _mapStrIdx(terminal: Terminal, lineIndex: number, rowIndex: number, stringIndex: number): [number, number] {\n    const buf = terminal.buffer.active;\n    const cell = buf.getNullCell();\n    let start = rowIndex;\n    while (stringIndex) {\n      const line = buf.getLine(lineIndex);\n      if (!line) {\n        return [-1, -1];\n      }\n      for (let i = start; i < line.length; ++i) {\n        line.getCell(i, cell);\n        const chars = cell.getChars();\n        const width = cell.getWidth();\n        if (width) {\n          stringIndex -= chars.length || 1;\n\n          // correct stringIndex for early wrapped wide chars:\n          // - currently only happens at last cell\n          // - cells to the right are reset with chars='' and width=1 in InputHandler.print\n          // - follow-up line must be wrapped and contain wide char at first cell\n          // --> if all these conditions are met, correct stringIndex by +1\n          if (i === line.length - 1 && chars === '') {\n            const line = buf.getLine(lineIndex + 1);\n            if (line && line.isWrapped) {\n              line.getCell(0, cell);\n              if (cell.getWidth() === 2) {\n                stringIndex += 1;\n              }\n            }\n          }\n        }\n        if (stringIndex < 0) {\n          return [lineIndex, i];\n        }\n      }\n      lineIndex++;\n      start = 0;\n    }\n    return [lineIndex, start];\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, ITerminalAddon, IDisposable } from '@xterm/xterm';\nimport type { WebLinksAddon as IWebLinksApi } from '@xterm/addon-web-links';\nimport { ILinkProviderOptions, WebLinkProvider } from './WebLinkProvider';\n\n// consider everthing starting with http:// or https://\n// up to first whitespace, `\"` or `'` as url\n// NOTE: The repeated end clause is needed to not match a dangling `:`\n// resembling the old (...)*([^:\"\\'\\\\s]) final path clause\n// additionally exclude early + final:\n// - unsafe from rfc3986: !*'()\n// - unsafe chars from rfc1738: {}|\\^~[]` (minus [] as we need them for ipv6 adresses, also allow ~)\n// also exclude as finals:\n// - final interpunction like ,.!?\n// - any sort of brackets <>()[]{} (not spec conform, but often used to enclose urls)\n// - unsafe chars from rfc1738: {}|\\^~[]`\nconst strictUrlRegex = /(https?|HTTPS?):[/]{2}[^\\s\"'!*(){}|\\\\\\^<>`]*[^\\s\"':,.!?{}|\\\\\\^~\\[\\]`()<>]/;\n\n\nfunction handleLink(event: MouseEvent, uri: string): void {\n  const newWindow = window.open();\n  if (newWindow) {\n    try {\n      newWindow.opener = null;\n    } catch {\n      // no-op, Electron can throw\n    }\n    newWindow.location.href = uri;\n  } else {\n    console.warn('Opening link blocked as opener could not be cleared');\n  }\n}\n\nexport class WebLinksAddon implements ITerminalAddon , IWebLinksApi {\n  private _terminal: Terminal | undefined;\n  private _linkProvider: IDisposable | undefined;\n\n  constructor(\n    private _handler: (event: MouseEvent, uri: string) => void = handleLink,\n    private _options: ILinkProviderOptions = {}\n  ) {\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    const options = this._options as ILinkProviderOptions;\n    const regex = options.urlRegex || strictUrlRegex;\n    this._linkProvider = this._terminal.registerLinkProvider(new WebLinkProvider(this._terminal, regex, this._handler, options));\n  }\n\n  public dispose(): void {\n    this._linkProvider?.dispose();\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "isUrl", "urlString", "url", "URL", "parsedBase", "password", "username", "protocol", "host", "toLocaleLowerCase", "startsWith", "e", "constructor", "_terminal", "_regex", "_handler", "_options", "provideLinks", "y", "callback", "links", "LinkComputer", "computeLink", "this", "_addCallbacks", "map", "link", "leave", "hover", "event", "uri", "range", "regex", "terminal", "activate", "rex", "RegExp", "source", "flags", "lines", "startLineIndex", "_getWindowedLineStrings", "line", "join", "match", "result", "exec", "text", "startY", "startX", "_mapStrIdx", "index", "endY", "endX", "length", "start", "x", "end", "push", "lineIndex", "topIdx", "bottomIdx", "content", "buffer", "active", "getLine", "currentC<PERSON>nt", "translateToString", "isWrapped", "indexOf", "reverse", "rowIndex", "stringIndex", "buf", "cell", "getNullCell", "i", "getCell", "chars", "getChars", "getWidth", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "strictUrlRegex", "handleLink", "newWindow", "window", "open", "opener", "location", "href", "console", "warn", "options", "urlRegex", "_linkProvider", "registerLinkProvider", "WebLinkProvider", "dispose"], "sourceRoot": ""}