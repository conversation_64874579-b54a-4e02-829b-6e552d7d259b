{"version": 3, "file": "TerminalAPI.js", "sourceRoot": "", "sources": ["../../src/api/TerminalAPI.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAatC;;;GAGG;AACH,MAAa,WAAY,SAAQ,qBAAY;IACzC,YAAoB,eAAgC;QAChD,KAAK,EAAE,CAAC;QADQ,oBAAe,GAAf,eAAe,CAAiB;QAEhD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,MAAuB;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACxE,OAAO,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAoB,EAAE,cAA+B;QACzE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzF,OAAO,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,cAAc,EAAE,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,UAAU,EAAE,QAAQ,CAAC,aAAa,EAAE;SACvC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC3D,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,cAAc,EAAE,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,UAAU,EAAE,QAAQ,CAAC,aAAa,EAAE;SACvC,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe;QACpD,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,KAAa;QAC7C,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB,EAAE,QAAgC;QAClE,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB;QAClC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,MAAM,MAAM,GAAG;YACX,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,eAAe;SAClB,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAhID,kCAgIC"}