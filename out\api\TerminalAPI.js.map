{"version": 3, "file": "TerminalAPI.js", "sourceRoot": "", "sources": ["../../src/api/TerminalAPI.ts"], "names": [], "mappings": ";;;AAGA;;;GAGG;AACH,MAAa,WAAW;IACpB,YAAoB,gBAAwC;QAAxC,qBAAgB,GAAhB,gBAAgB,CAAwB;IAAG,CAAC;IAEhE;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAA+B;QAChD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,OAA+B;QACjE,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,OAA+B;QAC1D,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAA+B;QACnD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAA+B;QACpD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,UAAkB,EAAE,IAAY,EAAE,aAAsB,IAAI;QACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,UAAkB;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,OAAe;QAC9C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAC9B,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;CACJ;AAtFD,kCAsFC"}