{"version": 3, "file": "TerminalAPI.js", "sourceRoot": "", "sources": ["../../src/api/TerminalAPI.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAoBtC;;;GAGG;AACH,MAAa,WAAY,SAAQ,qBAAY;IAIzC,YAAY,eAAgC;QACxC,KAAK,EAAE,CAAC;QAHJ,wBAAmB,GAAsC,IAAI,GAAG,EAAE,CAAC;QAIvE,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,MAAuB;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACxE,OAAO,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAoB,EAAE,cAA+B;QACzE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzF,OAAO,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,cAAc,EAAE,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,UAAU,EAAE,QAAQ,CAAC,aAAa,EAAE;SACvC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC3D,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;YAC5B,cAAc,EAAE,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,UAAU,EAAE,QAAQ,CAAC,aAAa,EAAE;SACvC,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe;QACpD,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,KAAa;QAC7C,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB,EAAE,QAAgC;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEjF,qBAAqB;QACrB,MAAM,YAAY,GAAuB;YACrC,UAAU;YACV,QAAQ;YACR,WAAW;SACd,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YAC3C,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7D,8BAA8B;QAC9B,OAAO,GAAG,EAAE;YACR,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACtD,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,QAAiD;QACvE,MAAM,MAAM,GAAG;YACX,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,gBAAgB;SACnB,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;gBACvC,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACR,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACnB,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB;QAClC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,IAAY,EAAE,IAAY;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,SAAS,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAAY,EAAE,WAAqB;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACtE,OAAO,OAAO,CAAC,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,cAAc;QACV,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC3B,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,OAAe,EAAE,UAAkB,KAAK;QACpF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC9D,IAAI,CAAC,QAAQ,EAAE;gBACX,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,UAAU,YAAY,CAAC,CAAC,CAAC;gBACtD,OAAO;aACV;YAED,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,aAA6B,CAAC;YAElC,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;gBACjC,MAAM,IAAI,IAAI,CAAC;YACnB,CAAC,CAAC;YAEF,MAAM,cAAc,GAAG,CAAC,SAA2B,EAAE,EAAE;gBACnD,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;oBAC3D,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;oBAClC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;oBAChD,YAAY,CAAC,aAAa,CAAC,CAAC;oBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBAC7B;YACL,CAAC,CAAC;YAEF,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACjC,QAAQ,CAAC,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAE/C,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAClC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,OAAO,IAAI,CAAC,CAAC,CAAC;YACtE,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,sCAAsC;QACtC,MAAM,MAAM,GAAG;YACX,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,gBAAgB;SACnB,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAAkB,EAAE,YAAgC;QAC3E,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,aAAa,EAAE;YACf,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAClD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;aAC/C;SACJ;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,UAAkB;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,aAAa,EAAE;YACf,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;YAChD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAC/C;IACL,CAAC;CACJ;AApTD,kCAoTC"}