// @ts-check

(function() {
    'use strict';

    // Get VS Code API
    const vscode = acquireVsCodeApi();

    // Terminal instances
    const terminals = new Map();
    let currentTerminalId = null;

    // DOM elements
    let terminalContainer;
    let terminalTabs;
    let createTerminalBtn;

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        console.log('Terminal DOM loaded, initializing...');
        initializeDOM();
        setupEventListeners();

        // Notify extension that webview is ready
        console.log('Sending ready message to extension');
        vscode.postMessage({ type: 'ready' });
    });

    function initializeDOM() {
        console.log('Initializing terminal DOM elements...');
        terminalContainer = document.getElementById('terminal-container');
        terminalTabs = document.getElementById('terminal-tabs');
        createTerminalBtn = document.getElementById('create-terminal-btn');

        console.log('Terminal DOM elements found:', {
            terminalContainer: !!terminalContainer,
            terminalTabs: !!terminalTabs,
            createTerminalBtn: !!createTerminalBtn
        });
    }

    function setupEventListeners() {
        // Create terminal button
        if (createTerminalBtn) {
            createTerminalBtn.addEventListener('click', () => {
                console.log('Create terminal button clicked');
                vscode.postMessage({ type: 'createTerminal' });
            });
        }

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            handleMessage(message);
        });
    }

    function handleMessage(message) {
        console.log('Terminal received message:', message);
        switch (message.type) {
            case 'terminalList':
                console.log('Updating terminal list:', message.terminals);
                updateTerminalList(message.terminals);
                break;

            case 'showTerminal':
                console.log('Showing terminal:', message.terminalId);
                showTerminal(message.terminalId);
                break;

            case 'terminalSelected':
                console.log('Terminal selected:', message.terminalId);
                selectTerminal(message.terminalId, message.output, message.history);
                break;

            case 'terminalData':
                console.log('Terminal data received for:', message.terminalId);
                handleTerminalData(message.terminalId, message.data);
                break;

            case 'terminalStatusChanged':
                console.log('Terminal status changed:', message.terminalId, message.status);
                updateTerminalStatus(message.terminalId, message.status);
                break;

            case 'commandExecuted':
                console.log('Command executed:', message.terminalId);
                handleCommandExecuted(message.terminalId, message.execution);
                break;
        }
    }

    function updateTerminalList(terminalList) {
        console.log('updateTerminalList called with:', terminalList);
        // Clear existing tabs
        terminalTabs.innerHTML = '';

        if (terminalList.length === 0) {
            // Show empty state
            showEmptyState();
        } else {
            hideEmptyState();
            terminalList.forEach(terminal => {
                createTerminalTab(terminal);
            });
        }
    }

    function showEmptyState() {
        console.log('Showing empty state');
        terminalContainer.innerHTML = `
            <div class="empty-state">
                <h3>No terminals created yet</h3>
                <p>Click "New Terminal" to get started</p>
            </div>
        `;
    }

    function hideEmptyState() {
        console.log('Hiding empty state');
        const emptyState = terminalContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }

    function createTerminalTab(terminal) {
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminal.id;

        const title = document.createElement('span');
        title.textContent = `Terminal (${terminal.status})`;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-btn';
        closeBtn.textContent = '×';
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            closeTerminal(terminal.id);
        });

        tab.appendChild(title);
        tab.appendChild(closeBtn);

        tab.addEventListener('click', () => {
            selectTerminalTab(terminal.id);
        });

        terminalTabs.appendChild(tab);
    }

    function selectTerminalTab(terminalId) {
        // Update tab selection
        document.querySelectorAll('.terminal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const selectedTab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Request terminal selection
        vscode.postMessage({
            type: 'selectTerminal',
            terminalId: terminalId
        });
    }

    function showTerminal(terminalId) {
        selectTerminalTab(terminalId);
    }

    function selectTerminal(terminalId, output, history) {
        currentTerminalId = terminalId;

        // Create or get terminal instance
        if (!terminals.has(terminalId)) {
            createTerminalInstance(terminalId);
        }

        const terminal = terminals.get(terminalId);

        // Clear and write output
        terminal.clear();
        if (output) {
            terminal.write(output);
        }

        // Show terminal
        showTerminalInstance(terminalId);
    }

    function createTerminalInstance(terminalId) {
        console.log('Creating xterm terminal instance for:', terminalId);

        // Create terminal wrapper
        const terminalWrapper = document.createElement('div');
        terminalWrapper.className = 'xterm-terminal-wrapper';
        terminalWrapper.style.display = 'none';

        // Create xterm terminal with VSCode-like theme
        const terminal = new Terminal({
            fontFamily: 'Consolas, "Courier New", monospace',
            fontSize: 14,
            lineHeight: 1.2,
            cursorBlink: true,
            cursorStyle: 'block',
            theme: {
                background: '#1e1e1e',
                foreground: '#cccccc',
                cursor: '#ffffff',
                cursorAccent: '#000000',
                selection: '#264f78',
                black: '#000000',
                red: '#cd3131',
                green: '#0dbc79',
                yellow: '#e5e510',
                blue: '#2472c8',
                magenta: '#bc3fbc',
                cyan: '#11a8cd',
                white: '#e5e5e5',
                brightBlack: '#666666',
                brightRed: '#f14c4c',
                brightGreen: '#23d18b',
                brightYellow: '#f5f543',
                brightBlue: '#3b8eea',
                brightMagenta: '#d670d6',
                brightCyan: '#29b8db',
                brightWhite: '#e5e5e5'
            },
            allowTransparency: true,
            scrollback: 1000,
            convertEol: true
        });

        // Open terminal in wrapper
        terminal.open(terminalWrapper);

        // Command line buffer
        let currentLine = '';
        let cursorPosition = 0;

        // Show initial prompt
        terminal.write('$ ');

        // Handle terminal input
        terminal.onData(data => {
            console.log('Terminal input data:', data, 'charCode:', data.charCodeAt(0));

            // Handle different input types
            for (let i = 0; i < data.length; i++) {
                const char = data[i];
                const charCode = char.charCodeAt(0);

                if (charCode === 13) { // Enter key
                    terminal.write('\r\n');
                    if (currentLine.trim()) {
                        console.log('Executing command:', currentLine);
                        vscode.postMessage({
                            type: 'executeCommand',
                            terminalId: terminalId,
                            command: currentLine.trim()
                        });
                    } else {
                        // Empty line, just show prompt
                        terminal.write('$ ');
                    }
                    currentLine = '';
                    cursorPosition = 0;
                } else if (charCode === 127 || charCode === 8) { // Backspace
                    if (cursorPosition > 0) {
                        currentLine = currentLine.slice(0, cursorPosition - 1) + currentLine.slice(cursorPosition);
                        cursorPosition--;
                        terminal.write('\b \b');
                    }
                } else if (charCode === 27) { // Escape sequences (arrow keys, etc.)
                    // Handle escape sequences for arrow keys, etc.
                    if (i + 2 < data.length && data[i + 1] === '[') {
                        const escapeCode = data[i + 2];
                        if (escapeCode === 'A') { // Up arrow
                            // TODO: Command history
                        } else if (escapeCode === 'B') { // Down arrow
                            // TODO: Command history
                        } else if (escapeCode === 'C') { // Right arrow
                            if (cursorPosition < currentLine.length) {
                                cursorPosition++;
                                terminal.write('\x1b[C');
                            }
                        } else if (escapeCode === 'D') { // Left arrow
                            if (cursorPosition > 0) {
                                cursorPosition--;
                                terminal.write('\x1b[D');
                            }
                        }
                        i += 2; // Skip the escape sequence
                    }
                } else if (charCode >= 32 && charCode <= 126) { // Printable characters
                    currentLine = currentLine.slice(0, cursorPosition) + char + currentLine.slice(cursorPosition);
                    cursorPosition++;
                    terminal.write(char);
                }
            }
        });

        // Handle terminal resize
        terminal.onResize(({ cols, rows }) => {
            console.log('Terminal resized:', cols, rows);
            vscode.postMessage({
                type: 'resizeTerminal',
                terminalId: terminalId,
                cols: cols,
                rows: rows
            });
        });

        // Add to container
        terminalContainer.appendChild(terminalWrapper);

        // Store terminal reference with additional properties
        terminal.currentLine = currentLine;
        terminal.cursorPosition = cursorPosition;
        terminals.set(terminalId, terminal);

        // Fit terminal to container
        setTimeout(() => {
            terminal.focus();
            fitTerminal(terminal);
        }, 100);
    }

    function showTerminalInstance(terminalId) {
        // Hide all terminals
        document.querySelectorAll('.xterm-terminal-wrapper').forEach(wrapper => {
            wrapper.style.display = 'none';
        });

        // Show selected terminal
        const terminal = terminals.get(terminalId);
        if (terminal && terminal.element && terminal.element.parentNode) {
            terminal.element.parentNode.style.display = 'block';
            // Focus and fit terminal
            setTimeout(() => {
                terminal.focus();
                fitTerminal(terminal);
            }, 100);
        }
    }

    function handleTerminalData(terminalId, data) {
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.write(data);
            // After command output, show new prompt
            if (data.includes('\n') || data.includes('\r')) {
                setTimeout(() => {
                    terminal.write('$ ');
                }, 100);
            }
        }
    }

    function updateTerminalStatus(terminalId, status) {
        const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (tab) {
            const title = tab.querySelector('span');
            if (title) {
                title.textContent = `Terminal (${status})`;
            }
        }
    }

    function handleCommandExecuted(terminalId, execution) {
        // Could add command execution indicators here
        console.log('Command executed:', execution);
    }

    function closeTerminal(terminalId) {
        vscode.postMessage({
            type: 'closeTerminal',
            terminalId: terminalId
        });

        // Clean up terminal instance
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.dispose();
            terminals.delete(terminalId);
        }

        // If this was the current terminal, clear current
        if (currentTerminalId === terminalId) {
            currentTerminalId = null;
        }
    }

    function fitTerminal(terminal) {
        if (terminal && terminal.element && terminal.element.parentNode) {
            const wrapper = terminal.element.parentNode;
            const rect = wrapper.getBoundingClientRect();
            const cols = Math.floor(rect.width / 9); // Approximate character width
            const rows = Math.floor(rect.height / 17); // Approximate line height

            if (cols > 0 && rows > 0) {
                terminal.resize(cols, rows);
            }
        }
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        if (currentTerminalId) {
            const terminal = terminals.get(currentTerminalId);
            if (terminal) {
                setTimeout(() => fitTerminal(terminal), 100);
            }
        }
    });

})();
