"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalAPI = void 0;
const events_1 = require("events");
/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
class TerminalAPI extends events_1.EventEmitter {
    constructor(terminalManager) {
        super();
        this.outputSubscriptions = new Map();
        this.terminalManager = terminalManager;
        this.setupEventForwarding();
    }
    /**
     * Create a new local terminal
     */
    async createLocalTerminal(config) {
        const terminal = await this.terminalManager.createLocalTerminal(config);
        return terminal.id;
    }
    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(sshConfig, terminalConfig) {
        const terminal = await this.terminalManager.createSSHTerminal(sshConfig, terminalConfig);
        return terminal.id;
    }
    /**
     * Get terminal information
     */
    getTerminalInfo(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (!terminal) {
            return null;
        }
        return {
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        };
    }
    /**
     * Get all terminals
     */
    getAllTerminals() {
        return this.terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        }));
    }
    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId, command) {
        await this.terminalManager.executeCommand(terminalId, command);
    }
    /**
     * Send input to terminal
     */
    async sendInput(terminalId, input) {
        await this.terminalManager.sendInput(terminalId, input);
    }
    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId) {
        return this.terminalManager.getTerminalOutput(terminalId);
    }
    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId, callback) {
        const unsubscribe = this.terminalManager.subscribeToOutput(terminalId, callback);
        // Track subscription
        const subscription = {
            terminalId,
            callback,
            unsubscribe
        };
        if (!this.outputSubscriptions.has(terminalId)) {
            this.outputSubscriptions.set(terminalId, []);
        }
        this.outputSubscriptions.get(terminalId).push(subscription);
        // Return unsubscribe function
        return () => {
            unsubscribe();
            this.removeSubscription(terminalId, subscription);
        };
    }
    /**
     * Subscribe to all terminal events
     */
    subscribeToTerminalEvents(callback) {
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError',
            'sessionCreated',
            'sessionDeleted'
        ];
        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                callback(event, ...args);
            });
        });
        return () => {
            events.forEach(event => {
                this.terminalManager.removeAllListeners(event);
            });
        };
    }
    /**
     * Close terminal
     */
    async closeTerminal(terminalId) {
        await this.terminalManager.closeTerminal(terminalId);
        this.cleanupSubscriptions(terminalId);
    }
    /**
     * Resize terminal
     */
    resizeTerminal(terminalId, cols, rows) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }
    /**
     * Clear terminal
     */
    clearTerminal(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }
    /**
     * Get terminal status
     */
    getTerminalStatus(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getStatus() : null;
    }
    /**
     * Check if terminal exists
     */
    terminalExists(terminalId) {
        return this.terminalManager.getTerminal(terminalId) !== undefined;
    }
    /**
     * Get command history for terminal
     */
    getCommandHistory(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getCommandHistory() : [];
    }
    /**
     * Get command executions for terminal
     */
    getCommandExecutions(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        return terminal ? terminal.getExecutions() : [];
    }
    /**
     * Create a new session
     */
    createSession(name, terminalIds) {
        const session = this.terminalManager.createSession(name, terminalIds);
        return session.id;
    }
    /**
     * Get session information
     */
    getSession(sessionId) {
        return this.terminalManager.getSession(sessionId) || null;
    }
    /**
     * Get all sessions
     */
    getAllSessions() {
        return this.terminalManager.getAllSessions();
    }
    /**
     * Delete session
     */
    deleteSession(sessionId) {
        this.terminalManager.deleteSession(sessionId);
    }
    /**
     * Execute command and wait for completion
     */
    async executeCommandAndWait(terminalId, command, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const terminal = this.terminalManager.getTerminal(terminalId);
            if (!terminal) {
                reject(new Error(`Terminal ${terminalId} not found`));
                return;
            }
            let output = '';
            let timeoutHandle;
            const dataHandler = (data) => {
                output += data;
            };
            const commandHandler = (execution) => {
                if (execution.command === command.trim() && execution.endTime) {
                    terminal.off('data', dataHandler);
                    terminal.off('commandExecuted', commandHandler);
                    clearTimeout(timeoutHandle);
                    resolve(execution.output);
                }
            };
            terminal.on('data', dataHandler);
            terminal.on('commandExecuted', commandHandler);
            timeoutHandle = setTimeout(() => {
                terminal.off('data', dataHandler);
                terminal.off('commandExecuted', commandHandler);
                reject(new Error(`Command execution timeout after ${timeout}ms`));
            }, timeout);
            this.executeCommand(terminalId, command).catch(reject);
        });
    }
    /**
     * Setup event forwarding from terminal manager
     */
    setupEventForwarding() {
        // Forward all terminal manager events
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError',
            'sessionCreated',
            'sessionDeleted'
        ];
        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                this.emit(event, ...args);
            });
        });
    }
    /**
     * Remove subscription
     */
    removeSubscription(terminalId, subscription) {
        const subscriptions = this.outputSubscriptions.get(terminalId);
        if (subscriptions) {
            const index = subscriptions.indexOf(subscription);
            if (index > -1) {
                subscriptions.splice(index, 1);
            }
            if (subscriptions.length === 0) {
                this.outputSubscriptions.delete(terminalId);
            }
        }
    }
    /**
     * Cleanup subscriptions for terminal
     */
    cleanupSubscriptions(terminalId) {
        const subscriptions = this.outputSubscriptions.get(terminalId);
        if (subscriptions) {
            subscriptions.forEach(sub => sub.unsubscribe());
            this.outputSubscriptions.delete(terminalId);
        }
    }
}
exports.TerminalAPI = TerminalAPI;
//# sourceMappingURL=TerminalAPI.js.map