"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalAPI = void 0;
const events_1 = require("events");
/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
class TerminalAPI extends events_1.EventEmitter {
    constructor(terminalManager) {
        super();
        this.terminalManager = terminalManager;
        this.setupEventForwarding();
    }
    /**
     * Create a new local terminal
     */
    async createLocalTerminal(config) {
        const terminal = await this.terminalManager.createLocalTerminal(config);
        return terminal.id;
    }
    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(sshConfig, terminalConfig) {
        const terminal = await this.terminalManager.createSSHTerminal(sshConfig, terminalConfig);
        return terminal.id;
    }
    /**
     * Get terminal information
     */
    getTerminalInfo(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (!terminal) {
            return null;
        }
        return {
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        };
    }
    /**
     * Get all terminals
     */
    getAllTerminals() {
        return this.terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus(),
            output: terminal.getOutput(),
            commandHistory: terminal.getCommandHistory(),
            executions: terminal.getExecutions()
        }));
    }
    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId, command) {
        await this.terminalManager.executeCommand(terminalId, command);
    }
    /**
     * Send input to terminal
     */
    async sendInput(terminalId, input) {
        await this.terminalManager.sendInput(terminalId, input);
    }
    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId) {
        return this.terminalManager.getTerminalOutput(terminalId);
    }
    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId, callback) {
        return this.terminalManager.subscribeToOutput(terminalId, callback);
    }
    /**
     * Close terminal
     */
    async closeTerminal(terminalId) {
        await this.terminalManager.closeTerminal(terminalId);
    }
    /**
     * Resize terminal
     */
    resizeTerminal(terminalId, cols, rows) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }
    /**
     * Clear terminal
     */
    clearTerminal(terminalId) {
        const terminal = this.terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }
    /**
     * Setup event forwarding from terminal manager
     */
    setupEventForwarding() {
        const events = [
            'terminalCreated',
            'terminalClosed',
            'terminalData',
            'terminalExit',
            'terminalError'
        ];
        events.forEach(event => {
            this.terminalManager.on(event, (...args) => {
                this.emit(event, ...args);
            });
        });
    }
}
exports.TerminalAPI = TerminalAPI;
//# sourceMappingURL=TerminalAPI.js.map