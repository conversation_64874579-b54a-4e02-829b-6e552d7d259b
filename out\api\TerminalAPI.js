"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalAPI = void 0;
/**
 * Public API for the Custom Terminal extension
 * This API can be used by other extensions to interact with terminals
 */
class TerminalAPI {
    constructor(terminalProvider) {
        this.terminalProvider = terminalProvider;
    }
    /**
     * Create a new terminal
     */
    async createTerminal(options) {
        return await this.terminalProvider.createTerminal(options);
    }
    /**
     * Create a new SSH terminal
     */
    async createSSHTerminal(host, options) {
        return await this.terminalProvider.createSSHTerminal(host, options);
    }
    /**
     * Create a PowerShell terminal
     */
    async createPowerShellTerminal(options) {
        return await this.terminalProvider.createPowerShellTerminal(options);
    }
    /**
     * Create a Command Prompt terminal
     */
    async createCmdTerminal(options) {
        return await this.terminalProvider.createCmdTerminal(options);
    }
    /**
     * Create a Bash terminal
     */
    async createBashTerminal(options) {
        return await this.terminalProvider.createBashTerminal(options);
    }
    /**
     * Get all terminals
     */
    getAllTerminals() {
        return this.terminalProvider.getAllTerminals();
    }
    /**
     * Get terminal by ID
     */
    getTerminalById(id) {
        return this.terminalProvider.getTerminalById(id);
    }
    /**
     * Send text to terminal
     */
    sendText(terminalId, text, addNewLine = true) {
        this.terminalProvider.sendText(terminalId, text, addNewLine);
    }
    /**
     * Show terminal
     */
    showTerminal(terminalId) {
        this.terminalProvider.showTerminal(terminalId);
    }
    /**
     * Execute command in terminal
     */
    executeCommand(terminalId, command) {
        this.terminalProvider.executeCommand(terminalId, command);
    }
    /**
     * Get terminal count
     */
    getTerminalCount() {
        return this.terminalProvider.getTerminalCount();
    }
    /**
     * Dispose terminal
     */
    disposeTerminal(terminalId) {
        this.terminalProvider.disposeTerminal(terminalId);
    }
}
exports.TerminalAPI = TerminalAPI;
//# sourceMappingURL=TerminalAPI.js.map