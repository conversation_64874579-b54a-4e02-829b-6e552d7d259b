import * as assert from 'assert';
import * as vscode from 'vscode';
import { TerminalManager } from '../terminal/TerminalManager';
import { TerminalAPI } from '../api/TerminalAPI';

suite('Terminal API Test Suite', () => {
    let terminalManager: TerminalManager;
    let terminalAPI: TerminalAPI;
    let mockContext: vscode.ExtensionContext;

    setup(() => {
        // Create mock context
        mockContext = {
            globalState: {
                keys: () => [],
                get: () => undefined,
                update: () => Promise.resolve()
            }
        } as any;

        terminalManager = new TerminalManager(mockContext);
        terminalAPI = new TerminalAPI(terminalManager);
    });

    teardown(async () => {
        if (terminalManager) {
            await terminalManager.dispose();
        }
    });

    test('Should create local terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        
        assert.ok(terminalId);
        assert.ok(terminalAPI.terminalExists(terminalId));
        
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.strictEqual(terminalInfo.type, 'local');
    });

    test('Should create SSH terminal', async () => {
        const sshConfig = {
            host: '<EMAIL>',
            port: 22
        };
        
        const terminalId = await terminalAPI.createSSHTerminal(sshConfig);
        
        assert.ok(terminalId);
        assert.ok(terminalAPI.terminalExists(terminalId));
        
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.strictEqual(terminalInfo.type, 'ssh');
    });

    test('Should execute command in terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        
        await terminalAPI.executeCommand(terminalId, 'help');
        
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.ok(terminalInfo.commandHistory.includes('help'));
    });

    test('Should get terminal output', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        
        const output = terminalAPI.getTerminalOutput(terminalId);
        assert.ok(output);
        assert.ok(output.includes('Custom Terminal'));
    });

    test('Should subscribe to terminal output', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        
        let receivedData = '';
        const unsubscribe = terminalAPI.subscribeToOutput(terminalId, (data) => {
            receivedData += data;
        });
        
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        
        await terminalAPI.executeCommand(terminalId, 'echo test');
        
        // Wait for command to execute
        await new Promise(resolve => setTimeout(resolve, 200));
        
        assert.ok(receivedData.includes('test'));
        
        unsubscribe();
    });

    test('Should manage terminal sessions', async () => {
        const terminalId1 = await terminalAPI.createLocalTerminal();
        const terminalId2 = await terminalAPI.createLocalTerminal();
        
        const sessionId = terminalAPI.createSession('Test Session', [terminalId1, terminalId2]);
        
        assert.ok(sessionId);
        
        const session = terminalAPI.getSession(sessionId);
        assert.ok(session);
        assert.strictEqual(session.name, 'Test Session');
        assert.strictEqual(session.terminalIds.length, 2);
        assert.ok(session.terminalIds.includes(terminalId1));
        assert.ok(session.terminalIds.includes(terminalId2));
    });

    test('Should close terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        
        assert.ok(terminalAPI.terminalExists(terminalId));
        
        await terminalAPI.closeTerminal(terminalId);
        
        assert.ok(!terminalAPI.terminalExists(terminalId));
    });

    test('Should get all terminals', async () => {
        const terminalId1 = await terminalAPI.createLocalTerminal();
        const terminalId2 = await terminalAPI.createSSHTerminal({ host: 'test', port: 22 });
        
        const terminals = terminalAPI.getAllTerminals();
        
        assert.strictEqual(terminals.length, 2);
        assert.ok(terminals.some(t => t.id === terminalId1));
        assert.ok(terminals.some(t => t.id === terminalId2));
    });

    test('Should handle terminal events', async () => {
        let eventReceived = false;
        let eventType = '';
        
        const unsubscribe = terminalAPI.subscribeToTerminalEvents((event, ...args) => {
            eventReceived = true;
            eventType = event;
        });
        
        await terminalAPI.createLocalTerminal();
        
        // Wait for event
        await new Promise(resolve => setTimeout(resolve, 100));
        
        assert.ok(eventReceived);
        assert.strictEqual(eventType, 'terminalCreated');
        
        unsubscribe();
    });
});
