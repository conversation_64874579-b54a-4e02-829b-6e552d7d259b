import * as vscode from 'vscode';
import * as os from 'os';
import * as path from 'path';

export interface CustomTerminalOptions {
    name?: string;
    cwd?: string;
    env?: { [key: string]: string };
    shellPath?: string;
    shellArgs?: string[];
}

export class CustomTerminalProvider {
    private terminals: Map<string, vscode.Terminal> = new Map();
    private terminalCounter = 0;
    private disposables: vscode.Disposable[] = [];

    constructor(private context: vscode.ExtensionContext) {
        // Listen for terminal close events
        this.disposables.push(
            vscode.window.onDidCloseTerminal((terminal) => {
                this.onTerminalClosed(terminal);
            })
        );

        // Listen for terminal open events
        this.disposables.push(
            vscode.window.onDidOpenTerminal((terminal) => {
                this.onTerminalOpened(terminal);
            })
        );
    }

    /**
     * Create a new custom terminal
     */
    async createTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        this.terminalCounter++;
        
        const terminalName = options?.name || `Custom Terminal ${this.terminalCounter}`;
        
        // Get default shell configuration
        const defaultShell = this.getDefaultShell();
        const shellPath = options?.shellPath || defaultShell.path;
        const shellArgs = options?.shellArgs || defaultShell.args;
        
        // Prepare environment variables
        const env = {
            ...process.env,
            ...options?.env,
            // Add custom environment variables for our terminal
            CUSTOM_TERMINAL: 'true',
            CUSTOM_TERMINAL_ID: `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        // Create terminal options
        const terminalOptions: vscode.TerminalOptions = {
            name: terminalName,
            shellPath: shellPath,
            shellArgs: shellArgs,
            cwd: options?.cwd || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || os.homedir(),
            env: env,
            iconPath: new vscode.ThemeIcon('terminal'),
            color: new vscode.ThemeColor('terminal.ansiBlue')
        };

        console.log('Creating terminal with options:', terminalOptions);

        // Create the terminal
        const terminal = vscode.window.createTerminal(terminalOptions);
        
        // Store terminal reference
        const terminalId = env.CUSTOM_TERMINAL_ID;
        this.terminals.set(terminalId, terminal);

        // Show the terminal
        terminal.show();

        // Send initial commands to set up the terminal
        this.setupTerminal(terminal);

        console.log(`Custom terminal created: ${terminalName} (ID: ${terminalId})`);
        
        return terminal;
    }

    /**
     * Create a terminal with SSH connection
     */
    async createSSHTerminal(host: string, options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        const sshOptions: CustomTerminalOptions = {
            ...options,
            name: `SSH: ${host}`,
            env: {
                ...options?.env,
                SSH_HOST: host
            }
        };

        const terminal = await this.createTerminal(sshOptions);
        
        // Send SSH command
        setTimeout(() => {
            terminal.sendText(`ssh ${host}`);
        }, 500);

        return terminal;
    }

    /**
     * Get all custom terminals
     */
    getAllTerminals(): vscode.Terminal[] {
        return Array.from(this.terminals.values()).filter(terminal => terminal.exitStatus === undefined);
    }

    /**
     * Get terminal by ID
     */
    getTerminalById(id: string): vscode.Terminal | undefined {
        return this.terminals.get(id);
    }

    /**
     * Send text to terminal
     */
    sendText(terminalId: string, text: string, addNewLine: boolean = true): void {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.sendText(text, addNewLine);
        }
    }

    /**
     * Show terminal
     */
    showTerminal(terminalId: string): void {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.show();
        }
    }

    /**
     * Hide terminal
     */
    hideTerminal(terminalId: string): void {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.hide();
        }
    }

    /**
     * Dispose terminal
     */
    disposeTerminal(terminalId: string): void {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            terminal.dispose();
            this.terminals.delete(terminalId);
        }
    }

    /**
     * Dispose all resources
     */
    dispose(): void {
        // Dispose all terminals
        for (const terminal of this.terminals.values()) {
            terminal.dispose();
        }
        this.terminals.clear();

        // Dispose event listeners
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }

    /**
     * Get default shell configuration
     */
    private getDefaultShell(): { path: string; args: string[] } {
        const platform = os.platform();
        
        // Get VSCode terminal configuration
        const config = vscode.workspace.getConfiguration('terminal.integrated');
        
        let shellPath: string;
        let shellArgs: string[] = [];

        if (platform === 'win32') {
            shellPath = config.get('shell.windows') || 
                       config.get('defaultProfile.windows') || 
                       'powershell.exe';
            shellArgs = config.get('shellArgs.windows') || [];
        } else if (platform === 'darwin') {
            shellPath = config.get('shell.osx') || 
                       config.get('defaultProfile.osx') || 
                       '/bin/zsh';
            shellArgs = config.get('shellArgs.osx') || [];
        } else {
            shellPath = config.get('shell.linux') || 
                       config.get('defaultProfile.linux') || 
                       '/bin/bash';
            shellArgs = config.get('shellArgs.linux') || [];
        }

        return { path: shellPath, args: shellArgs };
    }

    /**
     * Setup terminal with initial commands
     */
    private setupTerminal(terminal: vscode.Terminal): void {
        // Wait a bit for terminal to be ready, then send setup commands
        setTimeout(() => {
            // Send a welcome message
            terminal.sendText('echo "Welcome to Custom Terminal! Type \'help\' for available commands."');
            
            // You can add more setup commands here
            // For example, setting up aliases, environment variables, etc.
        }, 1000);
    }

    /**
     * Handle terminal closed event
     */
    private onTerminalClosed(terminal: vscode.Terminal): void {
        // Find and remove the terminal from our map
        for (const [id, storedTerminal] of this.terminals.entries()) {
            if (storedTerminal === terminal) {
                console.log(`Terminal closed: ${terminal.name} (ID: ${id})`);
                this.terminals.delete(id);
                break;
            }
        }
    }

    /**
     * Handle terminal opened event
     */
    private onTerminalOpened(terminal: vscode.Terminal): void {
        console.log(`Terminal opened: ${terminal.name}`);
    }

    /**
     * Execute command in terminal
     */
    executeCommand(terminalId: string, command: string): void {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.sendText(command);
        }
    }

    /**
     * Get terminal count
     */
    getTerminalCount(): number {
        return Array.from(this.terminals.values()).filter(terminal => terminal.exitStatus === undefined).length;
    }

    /**
     * Create terminal with specific shell
     */
    async createTerminalWithShell(shellPath: string, shellArgs?: string[], options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return this.createTerminal({
            ...options,
            shellPath,
            shellArgs
        });
    }

    /**
     * Create PowerShell terminal (Windows)
     */
    async createPowerShellTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return this.createTerminalWithShell('powershell.exe', [], {
            ...options,
            name: options?.name || 'PowerShell'
        });
    }

    /**
     * Create Command Prompt terminal (Windows)
     */
    async createCmdTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        return this.createTerminalWithShell('cmd.exe', [], {
            ...options,
            name: options?.name || 'Command Prompt'
        });
    }

    /**
     * Create Bash terminal
     */
    async createBashTerminal(options?: CustomTerminalOptions): Promise<vscode.Terminal> {
        const bashPath = os.platform() === 'win32' ? 'bash.exe' : '/bin/bash';
        return this.createTerminalWithShell(bashPath, [], {
            ...options,
            name: options?.name || 'Bash'
        });
    }
}
