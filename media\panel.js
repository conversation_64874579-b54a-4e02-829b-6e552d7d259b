// @ts-check

(function() {
    'use strict';

    // Get VS Code API
    const vscode = acquireVsCodeApi();

    // Terminal instances
    const terminals = new Map();
    let currentTerminalId = null;

    // DOM elements
    let terminalContainer;
    let terminalTabs;
    let createTerminalBtn;

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        console.log('Panel DOM loaded, initializing...');
        initializeDOM();
        setupEventListeners();
        
        // Notify extension that webview is ready
        console.log('Sending ready message to extension');
        vscode.postMessage({ type: 'ready' });
    });

    function initializeDOM() {
        console.log('Initializing panel DOM elements...');
        terminalContainer = document.getElementById('terminal-container');
        terminalTabs = document.getElementById('terminal-tabs');
        createTerminalBtn = document.getElementById('create-terminal-btn');
        
        console.log('Panel DOM elements found:', {
            terminalContainer: !!terminalContainer,
            terminalTabs: !!terminalTabs,
            createTerminalBtn: !!createTerminalBtn
        });
    }

    function setupEventListeners() {
        // Create terminal button
        if (createTerminalBtn) {
            createTerminalBtn.addEventListener('click', () => {
                console.log('Create terminal button clicked');
                vscode.postMessage({ type: 'createTerminal' });
            });
        }

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            handleMessage(message);
        });
    }

    function handleMessage(message) {
        console.log('Panel received message:', message);
        switch (message.type) {
            case 'terminalList':
                console.log('Updating terminal list:', message.terminals);
                updateTerminalList(message.terminals);
                break;
            
            case 'showTerminal':
                console.log('Showing terminal:', message.terminalId);
                showTerminal(message.terminalId);
                break;
            
            case 'terminalSelected':
                console.log('Terminal selected:', message.terminalId);
                selectTerminal(message.terminalId, message.output, message.history);
                break;
            
            case 'terminalData':
                console.log('Terminal data received for:', message.terminalId);
                handleTerminalData(message.terminalId, message.data);
                break;
            
            case 'terminalStatusChanged':
                console.log('Terminal status changed:', message.terminalId, message.status);
                updateTerminalStatus(message.terminalId, message.status);
                break;
            
            case 'commandExecuted':
                console.log('Command executed:', message.terminalId);
                handleCommandExecuted(message.terminalId, message.execution);
                break;
        }
    }

    function updateTerminalList(terminalList) {
        console.log('updateTerminalList called with:', terminalList);
        // Clear existing tabs
        terminalTabs.innerHTML = '';

        if (terminalList.length === 0) {
            // Show empty state
            showEmptyState();
        } else {
            hideEmptyState();
            terminalList.forEach(terminal => {
                createTerminalTab(terminal);
            });
        }
    }

    function showEmptyState() {
        console.log('Showing empty state');
        terminalContainer.innerHTML = `
            <div class="empty-state">
                <h3>No terminals created yet</h3>
                <p>Click "New Terminal" to get started</p>
            </div>
        `;
    }

    function hideEmptyState() {
        console.log('Hiding empty state');
        const emptyState = terminalContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }

    function createTerminalTab(terminal) {
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminal.id;
        
        const title = document.createElement('span');
        title.textContent = `Terminal (${terminal.status})`;
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-btn';
        closeBtn.textContent = '×';
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            closeTerminal(terminal.id);
        });

        tab.appendChild(title);
        tab.appendChild(closeBtn);
        
        tab.addEventListener('click', () => {
            selectTerminalTab(terminal.id);
        });

        terminalTabs.appendChild(tab);
    }

    function selectTerminalTab(terminalId) {
        // Update tab selection
        document.querySelectorAll('.terminal-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const selectedTab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Request terminal selection
        vscode.postMessage({
            type: 'selectTerminal',
            terminalId: terminalId
        });
    }

    function showTerminal(terminalId) {
        selectTerminalTab(terminalId);
    }

    function selectTerminal(terminalId, output, history) {
        currentTerminalId = terminalId;
        
        // Create or get terminal instance
        if (!terminals.has(terminalId)) {
            createTerminalInstance(terminalId);
        }

        const terminal = terminals.get(terminalId);
        
        // Clear and write output
        terminal.clear();
        if (output) {
            terminal.write(output);
        }

        // Show terminal
        showTerminalInstance(terminalId);
    }

    function createTerminalInstance(terminalId) {
        console.log('Creating terminal instance for:', terminalId);
        
        // Create terminal display area
        const terminalDiv = document.createElement('div');
        terminalDiv.className = 'simple-terminal';
        terminalDiv.style.cssText = `
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            height: calc(100% - 40px);
            overflow-y: auto;
            white-space: pre-wrap;
            border: none;
            outline: none;
        `;

        // Create input area
        const inputContainer = document.createElement('div');
        inputContainer.style.cssText = `
            display: flex;
            background: #2d2d30;
            border-top: 1px solid #3e3e42;
            padding: 5px;
            height: 40px;
        `;

        const inputField = document.createElement('input');
        inputField.type = 'text';
        inputField.placeholder = 'Type command and press Enter...';
        inputField.style.cssText = `
            flex: 1;
            background: #3c3c3c;
            color: #cccccc;
            border: 1px solid #464647;
            padding: 5px 10px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            outline: none;
        `;

        inputContainer.appendChild(inputField);

        const terminal = {
            element: terminalDiv,
            inputContainer: inputContainer,
            inputField: inputField,
            write: function(data) {
                terminalDiv.textContent += data;
                terminalDiv.scrollTop = terminalDiv.scrollHeight;
            },
            clear: function() {
                terminalDiv.textContent = '';
            },
            open: function(container) {
                const wrapper = document.createElement('div');
                wrapper.style.cssText = 'display: flex; flex-direction: column; height: 100%;';
                
                wrapper.appendChild(terminalDiv);
                wrapper.appendChild(inputContainer);
                container.appendChild(wrapper);
                
                // Focus input field
                setTimeout(() => inputField.focus(), 100);
            },
            dispose: function() {
                if (terminalDiv.parentNode) {
                    terminalDiv.parentNode.removeChild(terminalDiv);
                }
            }
        };

        // Handle input
        inputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const command = inputField.value;
                if (command.trim()) {
                    console.log('Executing command:', command);
                    // Send command
                    vscode.postMessage({
                        type: 'executeCommand',
                        terminalId: terminalId,
                        command: command
                    });
                    inputField.value = '';
                }
            }
        });

        terminal.open(terminalContainer);
        terminals.set(terminalId, terminal);
    }

    function showTerminalInstance(terminalId) {
        // Hide all terminals
        document.querySelectorAll('.simple-terminal').forEach(el => {
            if (el.parentNode) {
                el.parentNode.style.display = 'none';
            }
        });

        // Show selected terminal
        const terminal = terminals.get(terminalId);
        if (terminal && terminal.element.parentNode) {
            terminal.element.parentNode.style.display = 'flex';
            // Focus input field
            setTimeout(() => terminal.inputField.focus(), 100);
        }
    }

    function handleTerminalData(terminalId, data) {
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.write(data);
        }
    }

    function updateTerminalStatus(terminalId, status) {
        const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (tab) {
            const title = tab.querySelector('span');
            if (title) {
                title.textContent = `Terminal (${status})`;
            }
        }
    }

    function handleCommandExecuted(terminalId, execution) {
        // Could add command execution indicators here
        console.log('Command executed:', execution);
    }

    function closeTerminal(terminalId) {
        vscode.postMessage({
            type: 'closeTerminal',
            terminalId: terminalId
        });

        // Clean up terminal instance
        const terminal = terminals.get(terminalId);
        if (terminal) {
            terminal.dispose();
            terminals.delete(terminalId);
        }

        // If this was the current terminal, clear current
        if (currentTerminalId === terminalId) {
            currentTerminalId = null;
        }
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        // Could handle resize here if needed
    });

})();
