{"version": 3, "file": "LocalTerminal.js", "sourceRoot": "", "sources": ["../../src/terminal/LocalTerminal.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA2D;AAC3D,uCAAyB;AAGzB,yCAAqF;AAErF,MAAa,aAAc,SAAQ,mBAAQ;IACvC,YAAY,EAAU,EAAE,UAA2B,EAAE;QACjD,KAAK,CAAC,EAAE,EAAE,uBAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,YAAY,CAAC,CAAC;YAE5C,2DAA2D;YAC3D,kEAAkE;YAClE,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC5B;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,yBAAc,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC3B,mCAAmC;QACnC,UAAU,CAAC,GAAG,EAAE;YACZ,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3D,IAAI,CAAC,YAAY,IAAI,6BAA6B,MAAM,EAAE,CAAC;YAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,6BAA6B,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,eAAe;QACnB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAE/B,QAAQ,QAAQ,EAAE;YACd,KAAK,OAAO;gBACR,iCAAiC;gBACjC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBAC7E,IAAI,UAAU,EAAE;oBACZ,OAAO,UAAU,CAAC;iBACrB;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC;YAE5C,KAAK,QAAQ;gBACT,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC;YAE3C,KAAK,OAAO,CAAC;YACb;gBACI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;SAC/C;IACL,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,KAAK,CAAC,qBAAqB,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,wBAAwB;QACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YACpC,2DAA2D;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,OAAO,EAAE,EAAE,MAAM;gBAC5C,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe;QAChC,2BAA2B;QAC3B,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YACpC,OAAO;SACV;QAED,sCAAsC;QACtC,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAe;QAClD,yBAAyB;QACzB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE,EAAE;YAC1F,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5C;QAED,kCAAkC;QAClC,MAAM,SAAS,GAAQ;YACnB,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC9B,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,mBAAmB;QACnB,MAAM,UAAU,GAAG,GAAG,OAAO,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,IAAI,UAAU,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAE9B,8BAA8B;QAC9B,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAEzC,IAAI,GAAG,KAAK,MAAM,EAAE;gBAChB,MAAM,GAAG,qKAAqK,CAAC;aAClL;iBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAChC,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;aACtC;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;gBACvB,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC;aAC3C;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;gBACtB,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;aACnC;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK,EAAE;gBACtC,MAAM,GAAG,6EAA6E,CAAC;aAC1F;iBAAM,IAAI,GAAG,KAAK,EAAE,EAAE;gBACnB,MAAM,GAAG,sBAAsB,OAAO,MAAM,CAAC;aAChD;YAED,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC1B,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC;aAC9B;YAED,aAAa;YACb,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3D,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAE1B,qBAAqB;YACrB,SAAS,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAElC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAES,mBAAmB;QACzB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe;QACxC,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEpD,QAAQ,cAAc,EAAE;YACpB,KAAK,OAAO,CAAC;YACb,KAAK,KAAK;gBACN,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,wCAAwC;gBACxC,UAAU,CAAC,GAAG,EAAE;oBACZ,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC3D,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,sCAAsC;gBACvF,CAAC,EAAE,EAAE,CAAC,CAAC;gBACP,OAAO,IAAI,CAAC;YAEhB,KAAK,MAAM,CAAC;YACZ,KAAK,MAAM;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YAEhB;gBACI,OAAO,KAAK,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBAC9C,OAAO;aACV;YAED,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAe,CAAC;YAEpB,IAAI,QAAQ,KAAK,OAAO,EAAE;gBACtB,OAAO,GAAG,MAAM,CAAC;aACpB;iBAAM;gBACH,OAAO,GAAG,OAAO,CAAC;aACrB;YAED,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;gBACjC,MAAM,IAAI,IAAI,CAAC;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBAC5C,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;oBAC1C,gCAAgC;oBAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBACpC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAClF,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;iBAC7C;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAE/B,0BAA0B;YAC1B,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAC1C,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3B,CAAC,EAAE,IAAI,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB;QACnC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAe,CAAC;QAEpB,IAAI,QAAQ,KAAK,OAAO,EAAE;YACtB,OAAO,GAAG,UAAU,SAAS,GAAG,CAAC;SACpC;aAAM;YACH,OAAO,GAAG,OAAO,SAAS,GAAG,CAAC;SACjC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,SAAkB;QAClC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAe,CAAC;QAEpB,IAAI,QAAQ,KAAK,OAAO,EAAE;YACtB,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;SACtD;aAAM;YACH,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;SAC5D;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;CACJ;AAnQD,sCAmQC"}