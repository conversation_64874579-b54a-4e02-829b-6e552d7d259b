# 调试指南

## 问题分析

根据您的截图，插件显示了"Custom terminal created successfully!"的消息，但是没有看到终端界面。我已经添加了大量的调试信息来帮助定位问题。

## 调试步骤

### 1. 重新启动扩展
1. 在VSCode中按 `F5` 重新启动扩展开发主机
2. 在新的VSCode窗口中，查看是否出现了新的活动栏图标（终端图标）

### 2. 查看控制台日志
1. 在扩展开发主机窗口中，按 `F12` 打开开发者工具
2. 切换到 `Console` 标签
3. 执行创建终端命令
4. 查看控制台输出

### 3. 预期的日志输出

当您执行 "Custom Terminal: Create Custom Terminal" 命令时，应该看到以下日志：

```
Creating local terminal...
TerminalManager: Creating local terminal...
TerminalManager: Initializing terminal with ID: terminal_xxxxx
TerminalManager: Terminal created and stored, emitting event
Terminal created with ID: terminal_xxxxx
Showing terminal in webview...
showTerminal called with ID: terminal_xxxxx
```

如果WebView已加载，还应该看到：
```
Resolving webview view...
Setting webview HTML...
Sending initial terminal list...
Webview view resolved
DOM loaded, initializing...
Initializing DOM elements...
DOM elements found: {terminalContainer: true, terminalTabs: true, ...}
Sending ready message to extension
```

### 4. 查找Custom Terminal视图

#### 方法1：活动栏
- 查看VSCode左侧活动栏是否有新的终端图标
- 点击该图标应该打开Custom Terminal面板

#### 方法2：命令面板
1. 按 `Ctrl+Shift+P`
2. 输入 "View: Show Custom Terminal"
3. 选择该命令

#### 方法3：视图菜单
1. 点击 `View` 菜单
2. 查找 "Custom Terminal" 选项

### 5. 常见问题排查

#### 问题1：WebView没有加载
**症状**: 看不到任何WebView相关的日志
**解决**: 
- 检查package.json中的视图配置
- 重新加载扩展

#### 问题2：DOM元素未找到
**症状**: 日志显示DOM元素为false
**解决**: 
- 检查HTML模板
- 检查CSS文件是否正确加载

#### 问题3：终端创建成功但不显示
**症状**: 看到终端创建日志但没有UI更新
**解决**: 
- 检查WebView消息传递
- 检查前端JavaScript错误

### 6. 手动测试步骤

1. **重新启动扩展** (F5)
2. **打开开发者工具** (F12)
3. **查找Custom Terminal视图**:
   - 检查活动栏是否有终端图标
   - 如果没有，尝试 `View > Custom Terminal`
4. **创建终端**:
   - 使用命令面板: `Custom Terminal: Create Custom Terminal`
   - 或点击视图中的按钮
5. **查看日志输出**
6. **检查视图内容**

### 7. 可能的解决方案

#### 解决方案1：视图容器问题
如果活动栏没有显示图标，可能是视图容器配置问题。

#### 解决方案2：WebView权限问题
检查是否有安全策略阻止WebView加载。

#### 解决方案3：资源路径问题
检查CSS和JS文件路径是否正确。

### 8. 备用测试方法

如果视图仍然不显示，可以尝试：

1. **使用命令直接测试API**:
   ```javascript
   // 在开发者控制台中执行
   vscode.commands.executeCommand('customTerminal.createTerminal');
   ```

2. **检查扩展是否正确激活**:
   - 查看扩展列表中是否显示为已激活
   - 检查是否有错误消息

### 9. 报告问题时请提供

1. 控制台的完整日志输出
2. 是否看到Custom Terminal活动栏图标
3. 任何错误消息
4. VSCode版本信息

## 预期结果

正常工作时，您应该看到：
1. 活动栏中有一个终端图标
2. 点击图标打开Custom Terminal面板
3. 面板中有"New Local Terminal"和"New SSH Terminal"按钮
4. 点击按钮创建终端并显示在面板中
5. 可以在终端中输入命令并看到输出

请按照这些步骤进行调试，并告诉我您看到了什么日志输出！
