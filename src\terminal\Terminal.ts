import { EventEmitter } from 'events';
// import * as pty from 'node-pty'; // Temporarily disabled for initial implementation

export interface TerminalOptions {
    shell?: string;
    cwd?: string;
    env?: { [key: string]: string };
}

export enum TerminalType {
    LOCAL = 'local',
    SSH = 'ssh',
    TELNET = 'telnet',
    SERIAL = 'serial'
}

export enum TerminalStatus {
    INITIALIZING = 'initializing',
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
    ERROR = 'error',
    DISPOSED = 'disposed'
}

export interface CommandExecution {
    id: string;
    command: string;
    startTime: Date;
    endTime?: Date;
    exitCode?: number;
    output: string;
    error?: string;
}

export abstract class Terminal extends EventEmitter {
    public readonly id: string;
    public readonly type: TerminalType;
    protected ptyProcess?: any; // pty.IPty - temporarily using any
    protected status: TerminalStatus = TerminalStatus.INITIALIZING;
    protected outputBuffer: string = '';
    protected commandHistory: string[] = [];
    protected currentExecution?: CommandExecution;
    protected executions: CommandExecution[] = [];
    protected options: TerminalOptions;

    constructor(id: string, type: TerminalType, options: TerminalOptions = {}) {
        super();
        this.id = id;
        this.type = type;
        this.options = options;
    }

    /**
     * Initialize the terminal
     */
    abstract initialize(): Promise<void>;

    /**
     * Execute a command
     */
    async executeCommand(command: string): Promise<void> {
        if (this.status !== TerminalStatus.CONNECTED) {
            throw new Error('Terminal is not connected');
        }

        // Add to command history
        if (command.trim() && this.commandHistory[this.commandHistory.length - 1] !== command.trim()) {
            this.commandHistory.push(command.trim());
        }

        // Create command execution record
        const execution: CommandExecution = {
            id: this.generateExecutionId(),
            command: command.trim(),
            startTime: new Date(),
            output: ''
        };

        this.currentExecution = execution;
        this.executions.push(execution);

        // Send command to terminal
        await this.sendInput(command + '\r');

        this.emit('commandExecuted', execution);
    }

    /**
     * Send input to terminal
     */
    async sendInput(input: string): Promise<void> {
        if (this.status !== TerminalStatus.CONNECTED || !this.ptyProcess) {
            throw new Error('Terminal is not connected');
        }

        this.ptyProcess.write(input);
    }

    /**
     * Get terminal output
     */
    getOutput(): string {
        return this.outputBuffer;
    }

    /**
     * Get command history
     */
    getCommandHistory(): string[] {
        return [...this.commandHistory];
    }

    /**
     * Get command executions
     */
    getExecutions(): CommandExecution[] {
        return [...this.executions];
    }

    /**
     * Get current execution
     */
    getCurrentExecution(): CommandExecution | undefined {
        return this.currentExecution;
    }

    /**
     * Get terminal status
     */
    getStatus(): TerminalStatus {
        return this.status;
    }

    /**
     * Resize terminal
     */
    resize(cols: number, rows: number): void {
        if (this.ptyProcess) {
            this.ptyProcess.resize(cols, rows);
        }
    }

    /**
     * Clear terminal output
     */
    clear(): void {
        this.outputBuffer = '';
        this.emit('cleared');
    }

    /**
     * Kill terminal process
     */
    kill(): void {
        if (this.ptyProcess) {
            this.ptyProcess.kill();
        }
    }

    /**
     * Dispose terminal
     */
    async dispose(): Promise<void> {
        this.status = TerminalStatus.DISPOSED;

        if (this.ptyProcess) {
            this.ptyProcess.kill();
            this.ptyProcess = undefined;
        }

        this.removeAllListeners();
        this.emit('disposed');
    }

    /**
     * Setup PTY event handlers
     */
    protected setupPtyEventHandlers(): void {
        if (!this.ptyProcess) {
            return;
        }

        this.ptyProcess.onData((data: string) => {
            this.outputBuffer += data;

            // Update current execution output
            if (this.currentExecution) {
                this.currentExecution.output += data;
            }

            this.emit('data', data);
        });

        this.ptyProcess.onExit((exitCode: number) => {
            this.status = TerminalStatus.DISCONNECTED;

            // Complete current execution
            if (this.currentExecution) {
                this.currentExecution.endTime = new Date();
                this.currentExecution.exitCode = exitCode;
                this.currentExecution = undefined;
            }

            this.emit('exit', exitCode);
        });
    }

    /**
     * Set terminal status
     */
    protected setStatus(status: TerminalStatus): void {
        const oldStatus = this.status;
        this.status = status;
        this.emit('statusChanged', status, oldStatus);
    }

    /**
     * Generate execution ID
     */
    protected generateExecutionId(): string {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Trim output buffer to prevent memory issues
     */
    protected trimOutputBuffer(maxLines: number = 10000): void {
        const lines = this.outputBuffer.split('\n');
        if (lines.length > maxLines) {
            this.outputBuffer = lines.slice(-maxLines).join('\n');
        }
    }
}
