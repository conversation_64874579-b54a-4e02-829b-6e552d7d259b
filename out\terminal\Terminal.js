"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Terminal = exports.TerminalStatus = exports.TerminalType = void 0;
const events_1 = require("events");
var TerminalType;
(function (TerminalType) {
    TerminalType["LOCAL"] = "local";
    TerminalType["SSH"] = "ssh";
    TerminalType["TELNET"] = "telnet";
    TerminalType["SERIAL"] = "serial";
})(TerminalType = exports.TerminalType || (exports.TerminalType = {}));
var TerminalStatus;
(function (TerminalStatus) {
    TerminalStatus["INITIALIZING"] = "initializing";
    TerminalStatus["CONNECTED"] = "connected";
    TerminalStatus["DISCONNECTED"] = "disconnected";
    TerminalStatus["ERROR"] = "error";
    TerminalStatus["DISPOSED"] = "disposed";
})(TerminalStatus = exports.TerminalStatus || (exports.TerminalStatus = {}));
class Terminal extends events_1.EventEmitter {
    constructor(id, type, options = {}) {
        super();
        this.status = TerminalStatus.INITIALIZING;
        this.outputBuffer = '';
        this.commandHistory = [];
        this.executions = [];
        this.id = id;
        this.type = type;
        this.options = options;
    }
    /**
     * Execute a command
     */
    async executeCommand(command) {
        if (this.status !== TerminalStatus.CONNECTED) {
            throw new Error('Terminal is not connected');
        }
        // Add to command history
        if (command.trim() && this.commandHistory[this.commandHistory.length - 1] !== command.trim()) {
            this.commandHistory.push(command.trim());
        }
        // Create command execution record
        const execution = {
            id: this.generateExecutionId(),
            command: command.trim(),
            startTime: new Date(),
            output: ''
        };
        this.currentExecution = execution;
        this.executions.push(execution);
        // Send command to terminal
        await this.sendInput(command + '\r');
        this.emit('commandExecuted', execution);
    }
    /**
     * Send input to terminal
     */
    async sendInput(input) {
        if (this.status !== TerminalStatus.CONNECTED || !this.ptyProcess) {
            throw new Error('Terminal is not connected');
        }
        this.ptyProcess.write(input);
    }
    /**
     * Get terminal output
     */
    getOutput() {
        return this.outputBuffer;
    }
    /**
     * Get command history
     */
    getCommandHistory() {
        return [...this.commandHistory];
    }
    /**
     * Get command executions
     */
    getExecutions() {
        return [...this.executions];
    }
    /**
     * Get current execution
     */
    getCurrentExecution() {
        return this.currentExecution;
    }
    /**
     * Get terminal status
     */
    getStatus() {
        return this.status;
    }
    /**
     * Resize terminal
     */
    resize(cols, rows) {
        if (this.ptyProcess) {
            this.ptyProcess.resize(cols, rows);
        }
    }
    /**
     * Clear terminal output
     */
    clear() {
        this.outputBuffer = '';
        this.emit('cleared');
    }
    /**
     * Kill terminal process
     */
    kill() {
        if (this.ptyProcess) {
            this.ptyProcess.kill();
        }
    }
    /**
     * Dispose terminal
     */
    async dispose() {
        this.status = TerminalStatus.DISPOSED;
        if (this.ptyProcess) {
            this.ptyProcess.kill();
            this.ptyProcess = undefined;
        }
        this.removeAllListeners();
        this.emit('disposed');
    }
    /**
     * Setup PTY event handlers
     */
    setupPtyEventHandlers() {
        if (!this.ptyProcess) {
            return;
        }
        this.ptyProcess.onData((data) => {
            this.outputBuffer += data;
            // Update current execution output
            if (this.currentExecution) {
                this.currentExecution.output += data;
            }
            this.emit('data', data);
        });
        this.ptyProcess.onExit((exitCode) => {
            this.status = TerminalStatus.DISCONNECTED;
            // Complete current execution
            if (this.currentExecution) {
                this.currentExecution.endTime = new Date();
                this.currentExecution.exitCode = exitCode;
                this.currentExecution = undefined;
            }
            this.emit('exit', exitCode);
        });
    }
    /**
     * Set terminal status
     */
    setStatus(status) {
        const oldStatus = this.status;
        this.status = status;
        this.emit('statusChanged', status, oldStatus);
    }
    /**
     * Generate execution ID
     */
    generateExecutionId() {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Trim output buffer to prevent memory issues
     */
    trimOutputBuffer(maxLines = 10000) {
        const lines = this.outputBuffer.split('\n');
        if (lines.length > maxLines) {
            this.outputBuffer = lines.slice(-maxLines).join('\n');
        }
    }
}
exports.Terminal = Terminal;
//# sourceMappingURL=Terminal.js.map