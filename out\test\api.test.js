"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const TerminalManager_1 = require("../terminal/TerminalManager");
const TerminalAPI_1 = require("../api/TerminalAPI");
suite('Terminal API Test Suite', () => {
    let terminalManager;
    let terminalAPI;
    let mockContext;
    setup(() => {
        // Create mock context
        mockContext = {
            globalState: {
                keys: () => [],
                get: () => undefined,
                update: () => Promise.resolve()
            }
        };
        terminalManager = new TerminalManager_1.TerminalManager(mockContext);
        terminalAPI = new TerminalAPI_1.TerminalAPI(terminalManager);
    });
    teardown(async () => {
        if (terminalManager) {
            await terminalManager.dispose();
        }
    });
    test('Should create local terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        assert.ok(terminalId);
        assert.ok(terminalAPI.terminalExists(terminalId));
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.strictEqual(terminalInfo.type, 'local');
    });
    test('Should create SSH terminal', async () => {
        const sshConfig = {
            host: '<EMAIL>',
            port: 22
        };
        const terminalId = await terminalAPI.createSSHTerminal(sshConfig);
        assert.ok(terminalId);
        assert.ok(terminalAPI.terminalExists(terminalId));
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.strictEqual(terminalInfo.type, 'ssh');
    });
    test('Should execute command in terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        await terminalAPI.executeCommand(terminalId, 'help');
        const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
        assert.ok(terminalInfo);
        assert.ok(terminalInfo.commandHistory.includes('help'));
    });
    test('Should get terminal output', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        const output = terminalAPI.getTerminalOutput(terminalId);
        assert.ok(output);
        assert.ok(output.includes('Custom Terminal'));
    });
    test('Should subscribe to terminal output', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        let receivedData = '';
        const unsubscribe = terminalAPI.subscribeToOutput(terminalId, (data) => {
            receivedData += data;
        });
        // Wait for terminal to initialize
        await new Promise(resolve => setTimeout(resolve, 200));
        await terminalAPI.executeCommand(terminalId, 'echo test');
        // Wait for command to execute
        await new Promise(resolve => setTimeout(resolve, 200));
        assert.ok(receivedData.includes('test'));
        unsubscribe();
    });
    test('Should manage terminal sessions', async () => {
        const terminalId1 = await terminalAPI.createLocalTerminal();
        const terminalId2 = await terminalAPI.createLocalTerminal();
        const sessionId = terminalAPI.createSession('Test Session', [terminalId1, terminalId2]);
        assert.ok(sessionId);
        const session = terminalAPI.getSession(sessionId);
        assert.ok(session);
        assert.strictEqual(session.name, 'Test Session');
        assert.strictEqual(session.terminalIds.length, 2);
        assert.ok(session.terminalIds.includes(terminalId1));
        assert.ok(session.terminalIds.includes(terminalId2));
    });
    test('Should close terminal', async () => {
        const terminalId = await terminalAPI.createLocalTerminal();
        assert.ok(terminalAPI.terminalExists(terminalId));
        await terminalAPI.closeTerminal(terminalId);
        assert.ok(!terminalAPI.terminalExists(terminalId));
    });
    test('Should get all terminals', async () => {
        const terminalId1 = await terminalAPI.createLocalTerminal();
        const terminalId2 = await terminalAPI.createSSHTerminal({ host: 'test', port: 22 });
        const terminals = terminalAPI.getAllTerminals();
        assert.strictEqual(terminals.length, 2);
        assert.ok(terminals.some(t => t.id === terminalId1));
        assert.ok(terminals.some(t => t.id === terminalId2));
    });
    test('Should handle terminal events', async () => {
        let eventReceived = false;
        let eventType = '';
        const unsubscribe = terminalAPI.subscribeToTerminalEvents((event, ...args) => {
            eventReceived = true;
            eventType = event;
        });
        await terminalAPI.createLocalTerminal();
        // Wait for event
        await new Promise(resolve => setTimeout(resolve, 100));
        assert.ok(eventReceived);
        assert.strictEqual(eventType, 'terminalCreated');
        unsubscribe();
    });
});
//# sourceMappingURL=api.test.js.map