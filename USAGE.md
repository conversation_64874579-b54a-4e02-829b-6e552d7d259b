# VSCode Custom Terminal Plugin - 使用指南

## 快速开始

### 1. 安装和启动

1. 在VSCode中打开此项目
2. 按 `F5` 启动扩展开发主机
3. 在新的VSCode窗口中，您将看到扩展已加载

### 2. 打开自定义终端视图

- 在资源管理器面板中，您会看到一个新的 "Custom Terminal" 视图
- 点击展开该视图

### 3. 创建终端

#### 方法一：使用命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Custom Terminal"
3. 选择：
   - `Custom Terminal: Create Custom Terminal` - 创建本地终端
   - `Custom Terminal: Create SSH Terminal` - 创建SSH终端

#### 方法二：使用视图按钮
在Custom Terminal视图中，点击：
- "New Local Terminal" 按钮
- "New SSH Terminal" 按钮

### 4. 使用终端

#### 本地终端功能
```bash
# 查看可用命令
help

# 回显文本
echo "Hello World"

# 显示当前日期和时间
date

# 显示当前工作目录
pwd

# 列出目录内容
ls    # Linux/Mac
dir   # Windows

# 清屏
clear # Linux/Mac
cls   # Windows

# 退出终端
exit
```

#### SSH终端功能
创建SSH终端时：
1. 输入主机地址（如：`<EMAIL>`）
2. 输入端口号（默认：22）

SSH终端支持的命令：
```bash
help      # 显示帮助
whoami    # 显示当前用户
hostname  # 显示主机名
pwd       # 显示当前目录
ls        # 列出文件
exit      # 断开连接
```

### 5. 终端管理

#### 多终端切换
- 每个终端都有自己的标签页
- 点击标签页可以切换终端
- 标签页显示终端类型和状态

#### 关闭终端
- 点击标签页右侧的 "×" 按钮
- 或在终端中输入 `exit` 命令

#### 终端状态
- 🟡 `initializing` - 初始化中
- 🟢 `connected` - 已连接
- 🔴 `disconnected` - 已断开
- ❌ `error` - 错误状态

### 6. 配置选项

在VSCode设置中搜索 "Custom Terminal" 可以找到以下配置：

```json
{
  "customTerminal.defaultShell": "",
  "customTerminal.fontSize": 14,
  "customTerminal.fontFamily": "Consolas, 'Courier New', monospace",
  "customTerminal.theme": "dark",
  "customTerminal.maxOutputLines": 10000
}
```

### 7. 开发者功能

#### API使用
其他扩展可以通过以下方式使用Custom Terminal的API：

```typescript
// 获取扩展API
const customTerminal = vscode.extensions.getExtension('your-publisher.vscode-custom-terminal');
if (customTerminal) {
    const api = customTerminal.exports;
    
    // 创建终端
    const terminalId = await api.createLocalTerminal();
    
    // 执行命令
    await api.executeCommand(terminalId, 'echo "Hello from API"');
    
    // 监听输出
    api.subscribeToOutput(terminalId, (data) => {
        console.log('Output:', data);
    });
}
```

#### 事件监听
```typescript
// 监听终端事件
api.subscribeToTerminalEvents((event, ...args) => {
    console.log('Terminal event:', event, args);
});
```

### 8. 故障排除

#### 常见问题

**Q: 终端无法创建**
A: 确保VSCode版本 >= 1.74.0，检查扩展是否正确加载

**Q: 命令没有响应**
A: 检查终端状态是否为 "connected"，等待初始化完成

**Q: 找不到Custom Terminal视图**
A: 检查资源管理器面板，确保视图已展开

**Q: SSH连接失败**
A: 当前版本为模拟实现，用于演示基础功能

#### 调试方法
1. 打开VSCode开发者工具：`Help > Toggle Developer Tools`
2. 查看控制台错误信息
3. 检查扩展输出面板

### 9. 功能限制

当前版本的限制：
- SSH连接为模拟实现
- 不支持真实的shell进程（使用模拟命令）
- 不支持文件操作
- 不支持复杂的交互式程序

### 10. 下一步开发

计划中的功能：
- 真实的node-pty集成
- 完整的SSH/Telnet支持
- 文件传输功能
- 命令自动补全
- 语法高亮
- 主题自定义

### 11. 反馈和支持

如果您遇到问题或有建议：
1. 查看项目README.md
2. 查看DEMO.md了解更多功能
3. 提交Issue到项目仓库

## 总结

这个Custom Terminal插件提供了：
- ✅ 完整的插件架构
- ✅ 多终端管理
- ✅ 基础命令支持
- ✅ SSH终端模拟
- ✅ 公共API接口
- ✅ 事件系统
- ✅ 会话管理

虽然当前版本使用模拟实现，但架构设计完整，为后续集成真实终端功能奠定了坚实基础。
