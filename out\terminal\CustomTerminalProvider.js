"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomTerminalProvider = void 0;
const vscode = __importStar(require("vscode"));
const os = __importStar(require("os"));
class CustomTerminalProvider {
    constructor(context) {
        this.context = context;
        this.terminals = new Map();
        this.terminalCounter = 0;
        this.disposables = [];
        // Listen for terminal close events
        this.disposables.push(vscode.window.onDidCloseTerminal((terminal) => {
            this.onTerminalClosed(terminal);
        }));
        // Listen for terminal open events
        this.disposables.push(vscode.window.onDidOpenTerminal((terminal) => {
            this.onTerminalOpened(terminal);
        }));
    }
    /**
     * Create a new custom terminal
     */
    async createTerminal(options) {
        this.terminalCounter++;
        const terminalName = options?.name || `Custom Terminal ${this.terminalCounter}`;
        // Get default shell configuration
        const defaultShell = this.getDefaultShell();
        const shellPath = options?.shellPath || defaultShell.path;
        const shellArgs = options?.shellArgs || defaultShell.args;
        // Prepare environment variables
        const env = {
            ...process.env,
            ...options?.env,
            // Add custom environment variables for our terminal
            CUSTOM_TERMINAL: 'true',
            CUSTOM_TERMINAL_ID: `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        // Create terminal options
        const terminalOptions = {
            name: terminalName,
            shellPath: shellPath,
            shellArgs: shellArgs,
            cwd: options?.cwd || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || os.homedir(),
            env: env,
            iconPath: new vscode.ThemeIcon('terminal'),
            color: new vscode.ThemeColor('terminal.ansiBlue')
        };
        console.log('Creating terminal with options:', terminalOptions);
        // Create the terminal
        const terminal = vscode.window.createTerminal(terminalOptions);
        // Store terminal reference
        const terminalId = env.CUSTOM_TERMINAL_ID;
        this.terminals.set(terminalId, terminal);
        // Show the terminal
        terminal.show();
        // Send initial commands to set up the terminal
        this.setupTerminal(terminal);
        console.log(`Custom terminal created: ${terminalName} (ID: ${terminalId})`);
        return terminal;
    }
    /**
     * Create a terminal with SSH connection
     */
    async createSSHTerminal(host, options) {
        const sshOptions = {
            ...options,
            name: `SSH: ${host}`,
            env: {
                ...options?.env,
                SSH_HOST: host
            }
        };
        const terminal = await this.createTerminal(sshOptions);
        // Send SSH command
        setTimeout(() => {
            terminal.sendText(`ssh ${host}`);
        }, 500);
        return terminal;
    }
    /**
     * Get all custom terminals
     */
    getAllTerminals() {
        return Array.from(this.terminals.values()).filter(terminal => terminal.exitStatus === undefined);
    }
    /**
     * Get terminal by ID
     */
    getTerminalById(id) {
        return this.terminals.get(id);
    }
    /**
     * Send text to terminal
     */
    sendText(terminalId, text, addNewLine = true) {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.sendText(text, addNewLine);
        }
    }
    /**
     * Show terminal
     */
    showTerminal(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.show();
        }
    }
    /**
     * Hide terminal
     */
    hideTerminal(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.hide();
        }
    }
    /**
     * Dispose terminal
     */
    disposeTerminal(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            terminal.dispose();
            this.terminals.delete(terminalId);
        }
    }
    /**
     * Dispose all resources
     */
    dispose() {
        // Dispose all terminals
        for (const terminal of this.terminals.values()) {
            terminal.dispose();
        }
        this.terminals.clear();
        // Dispose event listeners
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
    /**
     * Get default shell configuration
     */
    getDefaultShell() {
        const platform = os.platform();
        // Get VSCode terminal configuration
        const config = vscode.workspace.getConfiguration('terminal.integrated');
        let shellPath;
        let shellArgs = [];
        if (platform === 'win32') {
            shellPath = config.get('shell.windows') ||
                config.get('defaultProfile.windows') ||
                'powershell.exe';
            shellArgs = config.get('shellArgs.windows') || [];
        }
        else if (platform === 'darwin') {
            shellPath = config.get('shell.osx') ||
                config.get('defaultProfile.osx') ||
                '/bin/zsh';
            shellArgs = config.get('shellArgs.osx') || [];
        }
        else {
            shellPath = config.get('shell.linux') ||
                config.get('defaultProfile.linux') ||
                '/bin/bash';
            shellArgs = config.get('shellArgs.linux') || [];
        }
        return { path: shellPath, args: shellArgs };
    }
    /**
     * Setup terminal with initial commands
     */
    setupTerminal(terminal) {
        // Wait a bit for terminal to be ready, then send setup commands
        setTimeout(() => {
            // Send a welcome message
            terminal.sendText('echo "Welcome to Custom Terminal! Type \'help\' for available commands."');
            // You can add more setup commands here
            // For example, setting up aliases, environment variables, etc.
        }, 1000);
    }
    /**
     * Handle terminal closed event
     */
    onTerminalClosed(terminal) {
        // Find and remove the terminal from our map
        for (const [id, storedTerminal] of this.terminals.entries()) {
            if (storedTerminal === terminal) {
                console.log(`Terminal closed: ${terminal.name} (ID: ${id})`);
                this.terminals.delete(id);
                break;
            }
        }
    }
    /**
     * Handle terminal opened event
     */
    onTerminalOpened(terminal) {
        console.log(`Terminal opened: ${terminal.name}`);
    }
    /**
     * Execute command in terminal
     */
    executeCommand(terminalId, command) {
        const terminal = this.terminals.get(terminalId);
        if (terminal && terminal.exitStatus === undefined) {
            terminal.sendText(command);
        }
    }
    /**
     * Get terminal count
     */
    getTerminalCount() {
        return Array.from(this.terminals.values()).filter(terminal => terminal.exitStatus === undefined).length;
    }
    /**
     * Create terminal with specific shell
     */
    async createTerminalWithShell(shellPath, shellArgs, options) {
        return this.createTerminal({
            ...options,
            shellPath,
            shellArgs
        });
    }
    /**
     * Create PowerShell terminal (Windows)
     */
    async createPowerShellTerminal(options) {
        return this.createTerminalWithShell('powershell.exe', [], {
            ...options,
            name: options?.name || 'PowerShell'
        });
    }
    /**
     * Create Command Prompt terminal (Windows)
     */
    async createCmdTerminal(options) {
        return this.createTerminalWithShell('cmd.exe', [], {
            ...options,
            name: options?.name || 'Command Prompt'
        });
    }
    /**
     * Create Bash terminal
     */
    async createBashTerminal(options) {
        const bashPath = os.platform() === 'win32' ? 'bash.exe' : '/bin/bash';
        return this.createTerminalWithShell(bashPath, [], {
            ...options,
            name: options?.name || 'Bash'
        });
    }
}
exports.CustomTerminalProvider = CustomTerminalProvider;
//# sourceMappingURL=CustomTerminalProvider.js.map