export interface SessionData {
    id: string;
    name: string;
    terminalIds: string[];
    createdAt: Date;
    lastAccessedAt: Date;
    metadata?: { [key: string]: any };
}

export class TerminalSession {
    public readonly id: string;
    public name: string;
    public terminalIds: string[];
    public createdAt: Date;
    public lastAccessedAt: Date;
    public metadata: { [key: string]: any };

    constructor(id: string, name: string, terminalIds: string[] = [], metadata: { [key: string]: any } = {}) {
        this.id = id;
        this.name = name;
        this.terminalIds = terminalIds;
        this.createdAt = new Date();
        this.lastAccessedAt = new Date();
        this.metadata = metadata;
    }

    /**
     * Add terminal to session
     */
    addTerminal(terminalId: string): void {
        if (!this.terminalIds.includes(terminalId)) {
            this.terminalIds.push(terminalId);
            this.updateLastAccessed();
        }
    }

    /**
     * Remove terminal from session
     */
    removeTerminal(terminalId: string): void {
        const index = this.terminalIds.indexOf(terminalId);
        if (index > -1) {
            this.terminalIds.splice(index, 1);
            this.updateLastAccessed();
        }
    }

    /**
     * Check if session contains terminal
     */
    hasTerminal(terminalId: string): boolean {
        return this.terminalIds.includes(terminalId);
    }

    /**
     * Get terminal count
     */
    getTerminalCount(): number {
        return this.terminalIds.length;
    }

    /**
     * Update last accessed time
     */
    updateLastAccessed(): void {
        this.lastAccessedAt = new Date();
    }

    /**
     * Set metadata
     */
    setMetadata(key: string, value: any): void {
        this.metadata[key] = value;
        this.updateLastAccessed();
    }

    /**
     * Get metadata
     */
    getMetadata(key: string): any {
        return this.metadata[key];
    }

    /**
     * Remove metadata
     */
    removeMetadata(key: string): void {
        delete this.metadata[key];
        this.updateLastAccessed();
    }

    /**
     * Convert to JSON for serialization
     */
    toJSON(): SessionData {
        return {
            id: this.id,
            name: this.name,
            terminalIds: [...this.terminalIds],
            createdAt: this.createdAt,
            lastAccessedAt: this.lastAccessedAt,
            metadata: { ...this.metadata }
        };
    }

    /**
     * Create from JSON data
     */
    static fromJSON(data: SessionData): TerminalSession {
        const session = new TerminalSession(data.id, data.name, data.terminalIds, data.metadata);
        session.createdAt = new Date(data.createdAt);
        session.lastAccessedAt = new Date(data.lastAccessedAt);
        return session;
    }

    /**
     * Clone session
     */
    clone(): TerminalSession {
        return TerminalSession.fromJSON(this.toJSON());
    }

    /**
     * Get session age in milliseconds
     */
    getAge(): number {
        return Date.now() - this.createdAt.getTime();
    }

    /**
     * Get time since last access in milliseconds
     */
    getTimeSinceLastAccess(): number {
        return Date.now() - this.lastAccessedAt.getTime();
    }

    /**
     * Check if session is empty
     */
    isEmpty(): boolean {
        return this.terminalIds.length === 0;
    }

    /**
     * Get session summary
     */
    getSummary(): string {
        const terminalCount = this.getTerminalCount();
        const age = Math.floor(this.getAge() / 1000 / 60); // minutes
        return `${this.name} (${terminalCount} terminals, ${age}m old)`;
    }
}
