"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalSession = void 0;
class TerminalSession {
    constructor(id, name, terminalIds = [], metadata = {}) {
        this.id = id;
        this.name = name;
        this.terminalIds = terminalIds;
        this.createdAt = new Date();
        this.lastAccessedAt = new Date();
        this.metadata = metadata;
    }
    /**
     * Add terminal to session
     */
    addTerminal(terminalId) {
        if (!this.terminalIds.includes(terminalId)) {
            this.terminalIds.push(terminalId);
            this.updateLastAccessed();
        }
    }
    /**
     * Remove terminal from session
     */
    removeTerminal(terminalId) {
        const index = this.terminalIds.indexOf(terminalId);
        if (index > -1) {
            this.terminalIds.splice(index, 1);
            this.updateLastAccessed();
        }
    }
    /**
     * Check if session contains terminal
     */
    hasTerminal(terminalId) {
        return this.terminalIds.includes(terminalId);
    }
    /**
     * Get terminal count
     */
    getTerminalCount() {
        return this.terminalIds.length;
    }
    /**
     * Update last accessed time
     */
    updateLastAccessed() {
        this.lastAccessedAt = new Date();
    }
    /**
     * Set metadata
     */
    setMetadata(key, value) {
        this.metadata[key] = value;
        this.updateLastAccessed();
    }
    /**
     * Get metadata
     */
    getMetadata(key) {
        return this.metadata[key];
    }
    /**
     * Remove metadata
     */
    removeMetadata(key) {
        delete this.metadata[key];
        this.updateLastAccessed();
    }
    /**
     * Convert to JSON for serialization
     */
    toJSON() {
        return {
            id: this.id,
            name: this.name,
            terminalIds: [...this.terminalIds],
            createdAt: this.createdAt,
            lastAccessedAt: this.lastAccessedAt,
            metadata: { ...this.metadata }
        };
    }
    /**
     * Create from JSON data
     */
    static fromJSON(data) {
        const session = new TerminalSession(data.id, data.name, data.terminalIds, data.metadata);
        session.createdAt = new Date(data.createdAt);
        session.lastAccessedAt = new Date(data.lastAccessedAt);
        return session;
    }
    /**
     * Clone session
     */
    clone() {
        return TerminalSession.fromJSON(this.toJSON());
    }
    /**
     * Get session age in milliseconds
     */
    getAge() {
        return Date.now() - this.createdAt.getTime();
    }
    /**
     * Get time since last access in milliseconds
     */
    getTimeSinceLastAccess() {
        return Date.now() - this.lastAccessedAt.getTime();
    }
    /**
     * Check if session is empty
     */
    isEmpty() {
        return this.terminalIds.length === 0;
    }
    /**
     * Get session summary
     */
    getSummary() {
        const terminalCount = this.getTerminalCount();
        const age = Math.floor(this.getAge() / 1000 / 60); // minutes
        return `${this.name} (${terminalCount} terminals, ${age}m old)`;
    }
}
exports.TerminalSession = TerminalSession;
//# sourceMappingURL=TerminalSession.js.map