import * as vscode from 'vscode';
import { TerminalManager } from './terminal/TerminalManager';
import { TerminalWebviewPanel } from './webview/TerminalWebviewPanel';
import { TerminalAPI } from './api/TerminalAPI';

let terminalManager: TerminalManager;
let terminalAPI: TerminalAPI;
let currentPanel: TerminalWebviewPanel | undefined;

export function activate(context: vscode.ExtensionContext) {
    console.log('Custom Terminal extension is now active!');

    // Initialize terminal manager
    terminalManager = new TerminalManager(context);

    // Initialize API
    terminalAPI = new TerminalAPI(terminalManager);

    // Register commands
    const createTerminalCommand = vscode.commands.registerCommand('customTerminal.createTerminal', async () => {
        try {
            console.log('Creating terminal...');

            // Create or show existing panel
            if (currentPanel) {
                currentPanel.reveal();
            } else {
                currentPanel = new TerminalWebviewPanel(context.extensionUri, terminalManager);
                currentPanel.onDidDispose(() => {
                    currentPanel = undefined;
                });
            }

            // Create a new terminal
            const terminal = await terminalManager.createLocalTerminal();
            console.log('Terminal created with ID:', terminal.id);

            // Show the terminal in the panel
            currentPanel.showTerminal(terminal.id);

            vscode.window.showInformationMessage('Terminal created successfully!');
            console.log('Terminal creation completed');
        } catch (error) {
            console.error('Failed to create terminal:', error);
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    });

    context.subscriptions.push(createTerminalCommand);

    // Export API for other extensions
    return terminalAPI;
}

export function deactivate() {
    if (terminalManager) {
        terminalManager.dispose();
    }
}
