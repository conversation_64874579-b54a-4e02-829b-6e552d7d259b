import * as vscode from 'vscode';
import { CustomTerminalProvider } from './terminal/CustomTerminalProvider';
import { TerminalAPI } from './api/TerminalAPI';

let customTerminalProvider: CustomTerminalProvider;
let terminalAPI: TerminalAPI;

export function activate(context: vscode.ExtensionContext) {
    console.log('Custom Terminal extension is now active!');

    // Initialize custom terminal provider
    customTerminalProvider = new CustomTerminalProvider(context);

    // Initialize API
    terminalAPI = new TerminalAPI(customTerminalProvider);

    // Register commands
    const createTerminalCommand = vscode.commands.registerCommand('customTerminal.createTerminal', async () => {
        try {
            console.log('Creating custom terminal...');

            // Create a new custom terminal
            const terminal = await customTerminalProvider.createTerminal();

            vscode.window.showInformationMessage('Custom Terminal created successfully!');
            console.log('Terminal creation completed');
        } catch (error) {
            console.error('Failed to create terminal:', error);
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    });

    context.subscriptions.push(createTerminalCommand);

    // Export API for other extensions
    return terminalAPI;
}

export function deactivate() {
    if (customTerminalProvider) {
        customTerminalProvider.dispose();
    }
}
