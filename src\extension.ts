import * as vscode from 'vscode';
import { TerminalManager } from './terminal/TerminalManager';
import { TerminalWebviewProvider } from './webview/TerminalWebviewProvider';
import { TerminalAPI } from './api/TerminalAPI';

let terminalManager: TerminalManager;
let terminalAPI: TerminalAPI;

export function activate(context: vscode.ExtensionContext) {
    console.log('Custom Terminal extension is now active!');

    // Initialize terminal manager
    terminalManager = new TerminalManager(context);
    
    // Initialize API
    terminalAPI = new TerminalAPI(terminalManager);
    
    // Register webview provider
    const webviewProvider = new TerminalWebviewProvider(context.extensionUri, terminalManager);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('customTerminal.terminalView', webviewProvider)
    );

    // Register commands
    const createTerminalCommand = vscode.commands.registerCommand('customTerminal.createTerminal', async () => {
        try {
            const terminal = await terminalManager.createLocalTerminal();
            webviewProvider.showTerminal(terminal.id);
            vscode.window.showInformationMessage('Custom Terminal created successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    });

    const createSSHTerminalCommand = vscode.commands.registerCommand('customTerminal.createSSHTerminal', async () => {
        try {
            // Get SSH connection details from user
            const host = await vscode.window.showInputBox({
                prompt: 'Enter SSH host (e.g., user@hostname)',
                placeHolder: 'user@hostname'
            });
            
            if (!host) {
                return;
            }

            const port = await vscode.window.showInputBox({
                prompt: 'Enter SSH port (default: 22)',
                placeHolder: '22'
            });

            const terminal = await terminalManager.createSSHTerminal({
                host,
                port: port ? parseInt(port) : 22
            });
            
            webviewProvider.showTerminal(terminal.id);
            vscode.window.showInformationMessage('SSH Terminal created successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create SSH terminal: ${error}`);
        }
    });

    context.subscriptions.push(createTerminalCommand, createSSHTerminalCommand);

    // Export API for other extensions
    return terminalAPI;
}

export function deactivate() {
    if (terminalManager) {
        terminalManager.dispose();
    }
}
