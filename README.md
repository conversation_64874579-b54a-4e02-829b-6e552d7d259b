# VSCode Custom Terminal Plugin

A VSCode extension that provides a custom terminal with real-time output capture, remote connection support, and API integration capabilities.

## 项目状态

这是一个完整的VSCode自定义终端插件实现，目前已完成基础架构和核心功能。

### 已实现功能

#### ✅ 第一阶段：基础架构
- [x] VSCode扩展项目结构
- [x] TypeScript配置
- [x] WebView架构
- [x] 基础依赖管理

#### ✅ 第二阶段：核心终端功能
- [x] 终端管理器 (TerminalManager)
- [x] 抽象终端基类 (Terminal)
- [x] 本地终端实现 (LocalTerminal)
- [x] SSH终端实现 (SSHTerminal)
- [x] 会话管理 (TerminalSession)

#### ✅ 第三阶段：用户界面
- [x] WebView提供者 (TerminalWebviewProvider)
- [x] 前端JavaScript界面
- [x] CSS样式
- [x] 终端标签页管理
- [x] 命令输入界面

#### ✅ 第四阶段：API接口
- [x] 公共API (TerminalAPI)
- [x] 事件系统
- [x] 输出订阅机制
- [x] 命令执行接口

### 当前实现特点

1. **模块化架构**: 清晰的类层次结构，易于扩展
2. **事件驱动**: 基于EventEmitter的事件系统
3. **实时输出**: 支持实时流式输出捕获
4. **多终端支持**: 本地终端和SSH终端
5. **会话管理**: 终端会话的创建、保存和恢复
6. **API集成**: 完整的API接口供其他插件使用

## 安装和使用

### 开发环境设置

1. 克隆项目到本地
2. 安装依赖：
   ```bash
   npm install
   ```
3. 编译TypeScript：
   ```bash
   npm run compile
   ```
4. 在VSCode中按F5启动调试

### 使用方法

1. 打开命令面板 (Ctrl+Shift+P)
2. 运行命令：
   - `Custom Terminal: Create Custom Terminal` - 创建本地终端
   - `Custom Terminal: Create SSH Terminal` - 创建SSH终端

### 功能演示

#### 本地终端
- 支持基本命令：`help`, `echo`, `date`, `pwd`, `ls/dir`, `clear`, `exit`
- 命令历史记录
- 实时输出显示

#### SSH终端
- 模拟SSH连接
- 支持基本远程命令
- 连接状态管理

## 技术架构

### 核心组件

```
src/
├── extension.ts              # 扩展入口点
├── terminal/
│   ├── TerminalManager.ts    # 终端管理器
│   ├── Terminal.ts           # 抽象终端基类
│   ├── LocalTerminal.ts      # 本地终端实现
│   ├── SSHTerminal.ts        # SSH终端实现
│   └── TerminalSession.ts    # 会话管理
├── webview/
│   └── TerminalWebviewProvider.ts  # WebView提供者
├── api/
│   └── TerminalAPI.ts        # 公共API接口
└── media/
    ├── main.js               # 前端JavaScript
    └── main.css              # 样式文件
```

### 设计模式

1. **工厂模式**: TerminalManager创建不同类型的终端
2. **观察者模式**: 事件驱动的架构
3. **策略模式**: 不同终端类型的不同实现策略
4. **单例模式**: TerminalManager的全局管理

## 下一步开发计划

### 第五阶段：真实终端集成
- [ ] 集成node-pty实现真实的进程执行
- [ ] 支持Windows/Linux/macOS平台
- [ ] 真实的SSH连接实现

### 第六阶段：高级功能
- [ ] 命令自动补全
- [ ] 语法高亮
- [ ] 输出格式化和分析
- [ ] 文件路径点击打开
- [ ] 搜索功能

### 第七阶段：远程连接增强
- [ ] Telnet支持
- [ ] 串行设备连接
- [ ] 断线重连机制
- [ ] 连接配置管理

### 第八阶段：用户体验
- [ ] 主题自定义
- [ ] 快捷键配置
- [ ] 状态栏集成
- [ ] 分割视图

## API使用示例

```typescript
// 获取插件API
const terminalAPI = vscode.extensions.getExtension('your-publisher.vscode-custom-terminal')?.exports;

// 创建终端
const terminalId = await terminalAPI.createLocalTerminal();

// 执行命令
await terminalAPI.executeCommand(terminalId, 'echo Hello World');

// 订阅输出
const unsubscribe = terminalAPI.subscribeToOutput(terminalId, (data) => {
    console.log('Terminal output:', data);
});

// 获取终端信息
const terminalInfo = terminalAPI.getTerminalInfo(terminalId);
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建Issue或联系开发者。
