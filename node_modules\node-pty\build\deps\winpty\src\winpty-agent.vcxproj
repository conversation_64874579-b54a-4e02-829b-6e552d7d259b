<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{446D3EF2-8BAE-9838-388D-A9912250AFEF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>winpty-agent</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\..\..\..\deps\winpty\src\bin\;$(MSBuildProjectDirectory)\..\..\..\..\deps\winpty\src\bin\</ExecutablePath>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName)$(TargetExt)</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>..\..\..\..\deps\winpty\src\gen;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\v8\include;..\..\..\..\deps\winpty\src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>UNICODE;_UNICODE;_WIN32_WINNT=0x0501;NOMINMAX;NODE_GYP_MODULE_NAME=winpty-agent;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;WINPTY_AGENT_ASSERT;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;gdi32.lib;winspool.lib;comdlg32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\20.15.0\\x64\\node.lib&quot;;advapi32.lib;shell32.lib;user32.lib</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName)$(TargetExt)</OutputFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>..\..\..\..\deps\winpty\src\gen;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\v8\include;..\..\..\..\deps\winpty\src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>UNICODE;_UNICODE;_WIN32_WINNT=0x0501;NOMINMAX;NODE_GYP_MODULE_NAME=winpty-agent;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;WINPTY_AGENT_ASSERT;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>..\..\..\..\deps\winpty\src\gen;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\v8\include;..\..\..\..\deps\winpty\src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>UNICODE;_UNICODE;_WIN32_WINNT=0x0501;NOMINMAX;NODE_GYP_MODULE_NAME=winpty-agent;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;WINPTY_AGENT_ASSERT;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;gdi32.lib;winspool.lib;comdlg32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\20.15.0\\x64\\node.lib&quot;;advapi32.lib;shell32.lib;user32.lib</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName)$(TargetExt)</OutputFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>..\..\..\..\deps\winpty\src\gen;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\20.15.0\deps\v8\include;..\..\..\..\deps\winpty\src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>UNICODE;_UNICODE;_WIN32_WINNT=0x0501;NOMINMAX;NODE_GYP_MODULE_NAME=winpty-agent;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;WINPTY_AGENT_ASSERT;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\..\..\deps\winpty\src\winpty.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Agent.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\AgentCreateDesktop.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleFont.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleInput.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleInputReencoding.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleLine.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Coord.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DebugShowInput.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DefaultInputMap.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DsrSender.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\EventLoop.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\InputMap.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\LargeConsoleRead.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\NamedPipe.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Scraper.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\SimplePool.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\SmallRect.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Terminal.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\UnicodeEncoding.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Win32Console.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Win32ConsoleBuffer.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\AgentMsg.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\BackgroundDesktop.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\Buffer.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\DebugClient.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\GenRandom.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\OsModule.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\OwnedHandle.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\StringBuilder.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\StringUtil.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\UnixCtrlChars.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WindowsSecurity.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WindowsVersion.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyAssert.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyException.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyVersion.h"/>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\winpty_snprintf.h"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Agent.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\Agent.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\AgentCreateDesktop.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\AgentCreateDesktop.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleFont.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\ConsoleFont.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleInput.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\ConsoleInput.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleInputReencoding.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\ConsoleInputReencoding.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleLine.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\ConsoleLine.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\DebugShowInput.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\DebugShowInput.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\DefaultInputMap.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\DefaultInputMap.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\EventLoop.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\EventLoop.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\InputMap.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\InputMap.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\LargeConsoleRead.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\LargeConsoleRead.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\NamedPipe.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\NamedPipe.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Scraper.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\Scraper.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Terminal.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\Terminal.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Win32Console.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\Win32Console.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Win32ConsoleBuffer.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\Win32ConsoleBuffer.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\main.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\agent\main.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\BackgroundDesktop.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\BackgroundDesktop.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\Buffer.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\Buffer.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\DebugClient.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\DebugClient.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\GenRandom.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\GenRandom.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\OwnedHandle.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\OwnedHandle.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\StringUtil.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\StringUtil.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WindowsSecurity.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\WindowsSecurity.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WindowsVersion.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\WindowsVersion.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyAssert.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\WinptyAssert.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyException.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\WinptyException.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyVersion.cc">
      <ObjectFileName>$(IntDir)\deps\winpty\src\shared\WinptyVersion.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="C:\Program Files\nodejs\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
