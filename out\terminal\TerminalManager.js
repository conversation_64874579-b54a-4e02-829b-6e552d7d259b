"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalManager = void 0;
const LocalTerminal_1 = require("./LocalTerminal");
const SSHTerminal_1 = require("./SSHTerminal");
const TerminalSession_1 = require("./TerminalSession");
const events_1 = require("events");
class TerminalManager extends events_1.EventEmitter {
    constructor(context) {
        super();
        this.terminals = new Map();
        this.sessions = new Map();
        this.context = context;
        this.loadSavedSessions();
    }
    /**
     * Create a local terminal
     */
    async createLocalTerminal(config) {
        const terminal = new LocalTerminal_1.LocalTerminal(this.generateId(), config);
        await terminal.initialize();
        this.terminals.set(terminal.id, terminal);
        this.setupTerminalEventHandlers(terminal);
        this.emit('terminalCreated', terminal);
        return terminal;
    }
    /**
     * Create an SSH terminal
     */
    async createSSHTerminal(sshConfig, terminalConfig) {
        const terminal = new SSHTerminal_1.SSHTerminal(this.generateId(), sshConfig, terminalConfig);
        await terminal.initialize();
        this.terminals.set(terminal.id, terminal);
        this.setupTerminalEventHandlers(terminal);
        this.emit('terminalCreated', terminal);
        return terminal;
    }
    /**
     * Get terminal by ID
     */
    getTerminal(id) {
        return this.terminals.get(id);
    }
    /**
     * Get all terminals
     */
    getAllTerminals() {
        return Array.from(this.terminals.values());
    }
    /**
     * Close terminal
     */
    async closeTerminal(id) {
        const terminal = this.terminals.get(id);
        if (terminal) {
            await terminal.dispose();
            this.terminals.delete(id);
            this.emit('terminalClosed', id);
        }
    }
    /**
     * Create a new session
     */
    createSession(name, terminalIds) {
        const session = new TerminalSession_1.TerminalSession(this.generateId(), name, terminalIds);
        this.sessions.set(session.id, session);
        this.saveSession(session);
        this.emit('sessionCreated', session);
        return session;
    }
    /**
     * Get session by ID
     */
    getSession(id) {
        return this.sessions.get(id);
    }
    /**
     * Get all sessions
     */
    getAllSessions() {
        return Array.from(this.sessions.values());
    }
    /**
     * Delete session
     */
    deleteSession(id) {
        const session = this.sessions.get(id);
        if (session) {
            this.sessions.delete(id);
            this.context.globalState.update(`session_${id}`, undefined);
            this.emit('sessionDeleted', id);
        }
    }
    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId, command) {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            await terminal.executeCommand(command);
        }
        else {
            throw new Error(`Terminal ${terminalId} not found`);
        }
    }
    /**
     * Send input to terminal
     */
    async sendInput(terminalId, input) {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            await terminal.sendInput(input);
        }
        else {
            throw new Error(`Terminal ${terminalId} not found`);
        }
    }
    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId) {
        const terminal = this.terminals.get(terminalId);
        return terminal ? terminal.getOutput() : '';
    }
    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId, callback) {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            terminal.on('data', callback);
            return () => terminal.off('data', callback);
        }
        return () => { };
    }
    /**
     * Dispose all terminals and sessions
     */
    async dispose() {
        for (const terminal of this.terminals.values()) {
            await terminal.dispose();
        }
        this.terminals.clear();
        this.sessions.clear();
    }
    generateId() {
        return `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    setupTerminalEventHandlers(terminal) {
        terminal.on('data', (data) => {
            this.emit('terminalData', terminal.id, data);
        });
        terminal.on('exit', (code) => {
            this.emit('terminalExit', terminal.id, code);
        });
        terminal.on('error', (error) => {
            this.emit('terminalError', terminal.id, error);
        });
    }
    async loadSavedSessions() {
        const sessionKeys = this.context.globalState.keys().filter(key => key.startsWith('session_'));
        for (const key of sessionKeys) {
            const sessionData = this.context.globalState.get(key);
            if (sessionData) {
                try {
                    const session = TerminalSession_1.TerminalSession.fromJSON(sessionData);
                    this.sessions.set(session.id, session);
                }
                catch (error) {
                    console.error(`Failed to load session ${key}:`, error);
                }
            }
        }
    }
    saveSession(session) {
        this.context.globalState.update(`session_${session.id}`, session.toJSON());
    }
}
exports.TerminalManager = TerminalManager;
//# sourceMappingURL=TerminalManager.js.map