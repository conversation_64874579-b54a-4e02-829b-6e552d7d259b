{"name": "vscode-custom-terminal", "displayName": "Custom Terminal", "description": "A custom terminal plugin with real-time output capture and remote connection support", "version": "0.1.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "customTerminal.createTerminal", "title": "Create Custom Terminal", "category": "Custom Terminal"}, {"command": "customTerminal.createSSHTerminal", "title": "Create SSH Terminal", "category": "Custom Terminal"}], "menus": {"commandPalette": [{"command": "customTerminal.createTerminal"}, {"command": "customTerminal.createSSHTerminal"}]}, "viewsContainers": {"activitybar": [{"id": "customTerminal", "title": "Custom Terminal", "icon": "$(terminal)"}]}, "views": {"customTerminal": [{"type": "webview", "id": "customTerminal.terminalView", "name": "Terminal", "when": "true"}]}, "configuration": {"title": "Custom Terminal", "properties": {"customTerminal.defaultShell": {"type": "string", "default": "", "description": "Default shell to use"}, "customTerminal.fontSize": {"type": "number", "default": 14, "description": "Terminal font size"}, "customTerminal.fontFamily": {"type": "string", "default": "Consolas, 'Courier New', monospace", "description": "Terminal font family"}, "customTerminal.theme": {"type": "string", "default": "dark", "enum": ["dark", "light"], "description": "Terminal theme"}, "customTerminal.maxOutputLines": {"type": "number", "default": 10000, "description": "Maximum number of output lines to keep in memory"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "test": "npm run compile && node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"@xterm/xterm": "^5.5.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-search": "^0.15.0"}}