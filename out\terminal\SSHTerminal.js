"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSHTerminal = void 0;
const Terminal_1 = require("./Terminal");
class SSHTerminal extends Terminal_1.Terminal {
    constructor(id, sshConfig, options = {}) {
        super(id, Terminal_1.TerminalType.SSH, options);
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 3;
        this.reconnectDelay = 5000; // 5 seconds
        this.sshConfig = sshConfig;
    }
    async initialize() {
        try {
            this.setStatus(Terminal_1.TerminalStatus.INITIALIZING);
            await this.simulateSSHConnection();
        }
        catch (error) {
            this.setStatus(Terminal_1.TerminalStatus.ERROR);
            this.emit('error', error);
            throw error;
        }
    }
    /**
     * Simulate SSH connection for initial implementation
     */
    async simulateSSHConnection() {
        // Simulate connection process
        setTimeout(() => {
            this.outputBuffer += `Connecting to ${this.sshConfig.host}...\r\n`;
            this.emit('data', `Connecting to ${this.sshConfig.host}...\r\n`);
        }, 100);
        setTimeout(() => {
            this.outputBuffer += `Connected to ${this.sshConfig.host}\r\n`;
            this.outputBuffer += `Welcome to SSH Terminal (simulated)\r\n`;
            this.outputBuffer += `user@${this.sshConfig.host}:~$ `;
            this.emit('data', `Connected to ${this.sshConfig.host}\r\n`);
            this.emit('data', `Welcome to SSH Terminal (simulated)\r\n`);
            this.emit('data', `user@${this.sshConfig.host}:~$ `);
            this.setStatus(Terminal_1.TerminalStatus.CONNECTED);
            this.emit('initialized');
            this.emit('connected');
        }, 500);
    }
    /**
     * Execute command in SSH terminal (simulated)
     */
    async executeCommand(command) {
        if (this.status !== Terminal_1.TerminalStatus.CONNECTED) {
            throw new Error('SSH Terminal is not connected');
        }
        // Add to command history
        if (command.trim() && this.commandHistory[this.commandHistory.length - 1] !== command.trim()) {
            this.commandHistory.push(command.trim());
        }
        // Echo the command
        const echoOutput = `${command}\r\n`;
        this.outputBuffer += echoOutput;
        this.emit('data', echoOutput);
        // Simulate command processing
        setTimeout(() => {
            let output = '';
            const cmd = command.trim().toLowerCase();
            if (cmd === 'help') {
                output = 'SSH Terminal Commands:\r\n  help - Show this help\r\n  whoami - Show current user\r\n  hostname - Show hostname\r\n  exit - Disconnect\r\n';
            }
            else if (cmd === 'whoami') {
                output = 'user\r\n';
            }
            else if (cmd === 'hostname') {
                output = this.sshConfig.host + '\r\n';
            }
            else if (cmd === 'pwd') {
                output = '/home/<USER>';
            }
            else if (cmd === 'ls') {
                output = 'documents  downloads  pictures\r\n';
            }
            else if (cmd === 'exit') {
                this.disconnect();
                return;
            }
            else if (cmd !== '') {
                output = `bash: ${command}: command not found\r\n`;
            }
            if (output) {
                this.outputBuffer += output;
                this.emit('data', output);
            }
            // Add prompt
            const prompt = `user@${this.sshConfig.host}:~$ `;
            this.outputBuffer += prompt;
            this.emit('data', prompt);
        }, 200);
    }
    /**
     * Disconnect from SSH server
     */
    async disconnect() {
        this.setStatus(Terminal_1.TerminalStatus.DISCONNECTED);
        this.outputBuffer += '\r\nConnection to ' + this.sshConfig.host + ' closed.\r\n';
        this.emit('data', '\r\nConnection to ' + this.sshConfig.host + ' closed.\r\n');
        this.emit('disconnected');
    }
    /**
     * Get SSH connection info
     */
    getConnectionInfo() {
        return { ...this.sshConfig };
    }
    /**
     * Update SSH configuration
     */
    updateSSHConfig(newConfig) {
        this.sshConfig = { ...this.sshConfig, ...newConfig };
    }
    /**
     * Check if terminal is connected
     */
    isConnected() {
        return this.status === Terminal_1.TerminalStatus.CONNECTED;
    }
    /**
     * Dispose SSH terminal
     */
    async dispose() {
        await this.disconnect();
        await super.dispose();
    }
}
exports.SSHTerminal = SSHTerminal;
//# sourceMappingURL=SSHTerminal.js.map