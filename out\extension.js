"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const TerminalManager_1 = require("./terminal/TerminalManager");
const TerminalWebviewProvider_1 = require("./webview/TerminalWebviewProvider");
const TerminalAPI_1 = require("./api/TerminalAPI");
let terminalManager;
let terminalAPI;
function activate(context) {
    console.log('Custom Terminal extension is now active!');
    // Initialize terminal manager
    terminalManager = new TerminalManager_1.TerminalManager(context);
    // Initialize API
    terminalAPI = new TerminalAPI_1.TerminalAPI(terminalManager);
    // Register webview provider
    const webviewProvider = new TerminalWebviewProvider_1.TerminalWebviewProvider(context.extensionUri, terminalManager);
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('customTerminal.terminalView', webviewProvider));
    // Register commands
    const createTerminalCommand = vscode.commands.registerCommand('customTerminal.createTerminal', async () => {
        try {
            console.log('Creating local terminal...');
            const terminal = await terminalManager.createLocalTerminal();
            console.log('Terminal created with ID:', terminal.id);
            console.log('Showing terminal in webview...');
            webviewProvider.showTerminal(terminal.id);
            vscode.window.showInformationMessage('Custom Terminal created successfully!');
            console.log('Terminal creation completed');
        }
        catch (error) {
            console.error('Failed to create terminal:', error);
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    });
    const createSSHTerminalCommand = vscode.commands.registerCommand('customTerminal.createSSHTerminal', async () => {
        try {
            // Get SSH connection details from user
            const host = await vscode.window.showInputBox({
                prompt: 'Enter SSH host (e.g., user@hostname)',
                placeHolder: 'user@hostname'
            });
            if (!host) {
                return;
            }
            const port = await vscode.window.showInputBox({
                prompt: 'Enter SSH port (default: 22)',
                placeHolder: '22'
            });
            const terminal = await terminalManager.createSSHTerminal({
                host,
                port: port ? parseInt(port) : 22
            });
            webviewProvider.showTerminal(terminal.id);
            vscode.window.showInformationMessage('SSH Terminal created successfully!');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create SSH terminal: ${error}`);
        }
    });
    context.subscriptions.push(createTerminalCommand, createSSHTerminalCommand);
    // Export API for other extensions
    return terminalAPI;
}
exports.activate = activate;
function deactivate() {
    if (terminalManager) {
        terminalManager.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map