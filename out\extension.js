"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const TerminalManager_1 = require("./terminal/TerminalManager");
const TerminalWebviewPanel_1 = require("./webview/TerminalWebviewPanel");
const TerminalAPI_1 = require("./api/TerminalAPI");
let terminalManager;
let terminalAPI;
let currentPanel;
function activate(context) {
    console.log('Custom Terminal extension is now active!');
    // Initialize terminal manager
    terminalManager = new TerminalManager_1.TerminalManager(context);
    // Initialize API
    terminalAPI = new TerminalAPI_1.TerminalAPI(terminalManager);
    // Register commands
    const createTerminalCommand = vscode.commands.registerCommand('customTerminal.createTerminal', async () => {
        try {
            console.log('Creating terminal...');
            // Create or show existing panel
            if (currentPanel) {
                currentPanel.reveal();
            }
            else {
                currentPanel = new TerminalWebviewPanel_1.TerminalWebviewPanel(context.extensionUri, terminalManager);
                currentPanel.onDidDispose(() => {
                    currentPanel = undefined;
                });
            }
            // Create a new terminal
            const terminal = await terminalManager.createLocalTerminal();
            console.log('Terminal created with ID:', terminal.id);
            // Show the terminal in the panel
            currentPanel.showTerminal(terminal.id);
            vscode.window.showInformationMessage('Terminal created successfully!');
            console.log('Terminal creation completed');
        }
        catch (error) {
            console.error('Failed to create terminal:', error);
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    });
    context.subscriptions.push(createTerminalCommand);
    // Export API for other extensions
    return terminalAPI;
}
exports.activate = activate;
function deactivate() {
    if (terminalManager) {
        terminalManager.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map