import * as vscode from 'vscode';
import { Terminal } from './Terminal';
import { LocalTerminal } from './LocalTerminal';
import { SSHTerminal } from './SSHTerminal';
import { TerminalSession } from './TerminalSession';
import { EventEmitter } from 'events';

export interface SSHConfig {
    host: string;
    port: number;
    username?: string;
    password?: string;
    privateKey?: string;
}

export interface TerminalConfig {
    shell?: string;
    cwd?: string;
    env?: { [key: string]: string };
}

export class TerminalManager extends EventEmitter {
    private terminals: Map<string, Terminal> = new Map();
    private sessions: Map<string, TerminalSession> = new Map();
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        super();
        this.context = context;
        this.loadSavedSessions();
    }

    /**
     * Create a local terminal
     */
    async createLocalTerminal(config?: TerminalConfig): Promise<Terminal> {
        console.log('TerminalManager: Creating local terminal...');
        const terminal = new LocalTerminal(this.generateId(), config);
        console.log('TerminalManager: Initializing terminal with ID:', terminal.id);
        await terminal.initialize();

        this.terminals.set(terminal.id, terminal);
        this.setupTerminalEventHandlers(terminal);

        console.log('TerminalManager: Terminal created and stored, emitting event');
        this.emit('terminalCreated', terminal);
        return terminal;
    }



    /**
     * Get terminal by ID
     */
    getTerminal(id: string): Terminal | undefined {
        return this.terminals.get(id);
    }

    /**
     * Get all terminals
     */
    getAllTerminals(): Terminal[] {
        return Array.from(this.terminals.values());
    }

    /**
     * Close terminal
     */
    async closeTerminal(id: string): Promise<void> {
        const terminal = this.terminals.get(id);
        if (terminal) {
            await terminal.dispose();
            this.terminals.delete(id);
            this.emit('terminalClosed', id);
        }
    }

    /**
     * Create a new session
     */
    createSession(name: string, terminalIds: string[]): TerminalSession {
        const session = new TerminalSession(this.generateId(), name, terminalIds);
        this.sessions.set(session.id, session);
        this.saveSession(session);
        this.emit('sessionCreated', session);
        return session;
    }

    /**
     * Get session by ID
     */
    getSession(id: string): TerminalSession | undefined {
        return this.sessions.get(id);
    }

    /**
     * Get all sessions
     */
    getAllSessions(): TerminalSession[] {
        return Array.from(this.sessions.values());
    }

    /**
     * Delete session
     */
    deleteSession(id: string): void {
        const session = this.sessions.get(id);
        if (session) {
            this.sessions.delete(id);
            this.context.globalState.update(`session_${id}`, undefined);
            this.emit('sessionDeleted', id);
        }
    }

    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId: string, command: string): Promise<void> {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            await terminal.executeCommand(command);
        } else {
            throw new Error(`Terminal ${terminalId} not found`);
        }
    }

    /**
     * Send input to terminal
     */
    async sendInput(terminalId: string, input: string): Promise<void> {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            await terminal.sendInput(input);
        } else {
            throw new Error(`Terminal ${terminalId} not found`);
        }
    }

    /**
     * Get terminal output
     */
    getTerminalOutput(terminalId: string): string {
        const terminal = this.terminals.get(terminalId);
        return terminal ? terminal.getOutput() : '';
    }

    /**
     * Subscribe to terminal output
     */
    subscribeToOutput(terminalId: string, callback: (data: string) => void): () => void {
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            terminal.on('data', callback);
            return () => terminal.off('data', callback);
        }
        return () => {};
    }

    /**
     * Dispose all terminals and sessions
     */
    async dispose(): Promise<void> {
        for (const terminal of this.terminals.values()) {
            await terminal.dispose();
        }
        this.terminals.clear();
        this.sessions.clear();
    }

    private generateId(): string {
        return `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private setupTerminalEventHandlers(terminal: Terminal): void {
        terminal.on('data', (data: string) => {
            this.emit('terminalData', terminal.id, data);
        });

        terminal.on('exit', (code: number) => {
            this.emit('terminalExit', terminal.id, code);
        });

        terminal.on('error', (error: Error) => {
            this.emit('terminalError', terminal.id, error);
        });
    }

    private async loadSavedSessions(): Promise<void> {
        const sessionKeys = this.context.globalState.keys().filter(key => key.startsWith('session_'));
        for (const key of sessionKeys) {
            const sessionData = this.context.globalState.get(key);
            if (sessionData) {
                try {
                    const session = TerminalSession.fromJSON(sessionData as any);
                    this.sessions.set(session.id, session);
                } catch (error) {
                    console.error(`Failed to load session ${key}:`, error);
                }
            }
        }
    }

    private saveSession(session: TerminalSession): void {
        this.context.globalState.update(`session_${session.id}`, session.toJSON());
    }
}
