# VSCode Custom Terminal Plugin - 演示指南

## 功能演示

### 1. 基础终端功能

#### 创建本地终端
1. 打开VSCode命令面板 (`Ctrl+Shift+P`)
2. 输入并选择 `Custom Terminal: Create Custom Terminal`
3. 新的终端标签页将出现在自定义终端视图中

#### 支持的命令
在本地终端中，您可以使用以下命令：

```bash
# 显示帮助信息
help

# 回显文本
echo Hello World

# 显示当前日期
date

# 显示当前工作目录
pwd

# 列出目录内容
ls
# 或在Windows上
dir

# 清屏
clear
# 或在Windows上
cls

# 退出终端
exit
```

### 2. SSH终端功能

#### 创建SSH终端
1. 打开VSCode命令面板 (`Ctrl+Shift+P`)
2. 输入并选择 `Custom Terminal: Create SSH Terminal`
3. 输入SSH主机地址 (例如: `<EMAIL>`)
4. 输入端口号 (默认: 22)
5. SSH终端将模拟连接过程

#### SSH终端命令
```bash
# 显示帮助
help

# 显示当前用户
whoami

# 显示主机名
hostname

# 显示当前目录
pwd

# 列出文件
ls

# 断开连接
exit
```

### 3. 多终端管理

#### 终端标签页
- 每个终端都有自己的标签页
- 点击标签页可以切换终端
- 点击标签页上的 "×" 按钮可以关闭终端
- 标签页显示终端类型和状态

#### 终端状态
- `initializing` - 初始化中
- `connected` - 已连接
- `disconnected` - 已断开
- `error` - 错误状态

### 4. API使用示例

#### 在其他扩展中使用

```typescript
// 获取Custom Terminal API
const customTerminalExt = vscode.extensions.getExtension('your-publisher.vscode-custom-terminal');
if (customTerminalExt) {
    const terminalAPI = customTerminalExt.exports;
    
    // 创建本地终端
    const localTerminalId = await terminalAPI.createLocalTerminal({
        shell: 'bash',
        cwd: '/home/<USER>'
    });
    
    // 创建SSH终端
    const sshTerminalId = await terminalAPI.createSSHTerminal({
        host: '<EMAIL>',
        port: 22
    });
    
    // 执行命令
    await terminalAPI.executeCommand(localTerminalId, 'ls -la');
    
    // 订阅输出
    const unsubscribe = terminalAPI.subscribeToOutput(localTerminalId, (data) => {
        console.log('Terminal output:', data);
    });
    
    // 获取终端信息
    const terminalInfo = terminalAPI.getTerminalInfo(localTerminalId);
    console.log('Terminal type:', terminalInfo.type);
    console.log('Terminal status:', terminalInfo.status);
    console.log('Command history:', terminalInfo.commandHistory);
    
    // 创建会话
    const sessionId = terminalAPI.createSession('My Session', [localTerminalId, sshTerminalId]);
    
    // 获取所有终端
    const allTerminals = terminalAPI.getAllTerminals();
    
    // 关闭终端
    await terminalAPI.closeTerminal(localTerminalId);
    
    // 取消订阅
    unsubscribe();
}
```

#### 事件监听

```typescript
// 监听所有终端事件
const unsubscribeEvents = terminalAPI.subscribeToTerminalEvents((event, ...args) => {
    switch (event) {
        case 'terminalCreated':
            console.log('Terminal created:', args[0]);
            break;
        case 'terminalClosed':
            console.log('Terminal closed:', args[0]);
            break;
        case 'terminalData':
            console.log('Terminal data:', args[0], args[1]);
            break;
        case 'terminalError':
            console.log('Terminal error:', args[0], args[1]);
            break;
    }
});
```

### 5. 配置选项

在VSCode设置中，您可以配置以下选项：

```json
{
    "customTerminal.defaultShell": "",
    "customTerminal.fontSize": 14,
    "customTerminal.fontFamily": "Consolas, 'Courier New', monospace",
    "customTerminal.theme": "dark",
    "customTerminal.maxOutputLines": 10000
}
```

### 6. 开发和调试

#### 启动开发环境
1. 克隆项目
2. 运行 `npm install`
3. 运行 `npm run compile`
4. 在VSCode中按 `F5` 启动调试

#### 查看日志
- 打开VSCode开发者工具 (`Help > Toggle Developer Tools`)
- 查看控制台输出
- 检查网络请求和错误

### 7. 故障排除

#### 常见问题

**问题**: 终端无法创建
**解决**: 检查VSCode版本是否支持，确保扩展已正确安装

**问题**: 命令无响应
**解决**: 检查终端状态，确保终端已连接

**问题**: SSH连接失败
**解决**: 当前版本为模拟实现，真实SSH功能将在后续版本中提供

#### 调试技巧
1. 查看VSCode输出面板中的扩展日志
2. 使用浏览器开发者工具检查WebView
3. 检查终端状态和事件

### 8. 性能优化

#### 大量输出处理
- 插件自动限制输出缓冲区大小
- 超过限制时会自动清理旧数据
- 可通过配置调整最大行数

#### 内存管理
- 终端关闭时会自动清理资源
- 事件监听器会在适当时机移除
- 定期检查和清理无用数据

### 9. 扩展开发

#### 添加新终端类型
1. 继承 `Terminal` 基类
2. 实现 `initialize()` 方法
3. 在 `TerminalManager` 中添加创建方法

#### 添加新功能
1. 在相应的类中添加方法
2. 更新API接口
3. 添加前端支持
4. 编写测试用例

### 10. 未来计划

- 真实的node-pty集成
- 完整的SSH/Telnet支持
- 命令自动补全
- 语法高亮
- 主题自定义
- 更多配置选项

## 反馈和贡献

欢迎提交Issue和Pull Request来改进这个插件！
