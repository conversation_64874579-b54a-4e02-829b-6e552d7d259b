import * as vscode from 'vscode';
import { TerminalManager } from '../terminal/TerminalManager';
import { Terminal } from '../terminal/Terminal';

export class TerminalWebviewPanel {
    public static readonly viewType = 'customTerminal.terminalPanel';
    
    private _panel: vscode.WebviewPanel;
    private _currentTerminalId?: string;
    private _disposables: vscode.Disposable[] = [];

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _terminalManager: TerminalManager
    ) {
        // Create webview panel
        this._panel = vscode.window.createWebviewPanel(
            TerminalWebviewPanel.viewType,
            'Custom Terminal',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [this._extensionUri],
                retainContextWhenHidden: true
            }
        );

        // Set HTML content
        this._panel.webview.html = this._getHtmlForWebview(this._panel.webview);

        // Handle messages from webview
        this._panel.webview.onDidReceiveMessage(
            message => {
                console.log('Panel received message:', message);
                this.handleWebviewMessage(message);
            },
            null,
            this._disposables
        );

        // Handle panel disposal
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // Setup terminal manager events
        this.setupTerminalManagerEvents();

        // Send initial state
        this.sendTerminalList();
    }

    /**
     * Show specific terminal
     */
    public showTerminal(terminalId: string): void {
        console.log('Panel showTerminal called with ID:', terminalId);
        this._currentTerminalId = terminalId;
        this._panel.webview.postMessage({
            type: 'showTerminal',
            terminalId: terminalId
        });
    }

    /**
     * Reveal the panel
     */
    public reveal(): void {
        this._panel.reveal();
    }

    /**
     * Handle disposal
     */
    public dispose(): void {
        this._panel.dispose();
        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }

    /**
     * Get onDidDispose event
     */
    public onDidDispose(listener: () => void): vscode.Disposable {
        return this._panel.onDidDispose(listener);
    }

    /**
     * Handle messages from webview
     */
    private handleWebviewMessage(message: any): void {
        switch (message.type) {
            case 'ready':
                this.sendTerminalList();
                break;

            case 'selectTerminal':
                this.selectTerminal(message.terminalId);
                break;

            case 'executeCommand':
                this.executeCommand(message.terminalId, message.command);
                break;

            case 'resizeTerminal':
                this.resizeTerminal(message.terminalId, message.cols, message.rows);
                break;

            case 'clearTerminal':
                this.clearTerminal(message.terminalId);
                break;

            case 'closeTerminal':
                this.closeTerminal(message.terminalId);
                break;

            case 'createTerminal':
                this.createTerminal();
                break;
        }
    }

    /**
     * Setup terminal manager event handlers
     */
    private setupTerminalManagerEvents(): void {
        this._terminalManager.on('terminalCreated', (terminal: Terminal) => {
            this.sendTerminalList();
            this.setupTerminalEvents(terminal);
        });

        this._terminalManager.on('terminalClosed', (terminalId: string) => {
            this.sendTerminalList();
            if (this._currentTerminalId === terminalId) {
                this._currentTerminalId = undefined;
            }
        });

        this._terminalManager.on('terminalData', (terminalId: string, data: string) => {
            this._panel.webview.postMessage({
                type: 'terminalData',
                terminalId: terminalId,
                data: data
            });
        });
    }

    /**
     * Setup events for a specific terminal
     */
    private setupTerminalEvents(terminal: Terminal): void {
        terminal.on('statusChanged', (status) => {
            this._panel.webview.postMessage({
                type: 'terminalStatusChanged',
                terminalId: terminal.id,
                status: status
            });
        });

        terminal.on('commandExecuted', (execution) => {
            this._panel.webview.postMessage({
                type: 'commandExecuted',
                terminalId: terminal.id,
                execution: execution
            });
        });
    }

    /**
     * Send terminal list to webview
     */
    private sendTerminalList(): void {
        const terminals = this._terminalManager.getAllTerminals().map(terminal => ({
            id: terminal.id,
            type: terminal.type,
            status: terminal.getStatus()
        }));

        console.log('Panel sending terminal list:', terminals);
        this._panel.webview.postMessage({
            type: 'terminalList',
            terminals: terminals
        });
    }

    /**
     * Select terminal
     */
    private selectTerminal(terminalId: string): void {
        this._currentTerminalId = terminalId;
        const terminal = this._terminalManager.getTerminal(terminalId);
        
        if (terminal) {
            this._panel.webview.postMessage({
                type: 'terminalSelected',
                terminalId: terminalId,
                output: terminal.getOutput(),
                history: terminal.getCommandHistory()
            });
        }
    }

    /**
     * Execute command in terminal
     */
    private async executeCommand(terminalId: string, command: string): Promise<void> {
        try {
            await this._terminalManager.executeCommand(terminalId, command);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute command: ${error}`);
        }
    }

    /**
     * Resize terminal
     */
    private resizeTerminal(terminalId: string, cols: number, rows: number): void {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }

    /**
     * Clear terminal
     */
    private clearTerminal(terminalId: string): void {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }

    /**
     * Close terminal
     */
    private async closeTerminal(terminalId: string): Promise<void> {
        try {
            await this._terminalManager.closeTerminal(terminalId);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to close terminal: ${error}`);
        }
    }

    /**
     * Create new terminal
     */
    private async createTerminal(): Promise<void> {
        try {
            const terminal = await this._terminalManager.createLocalTerminal();
            this.showTerminal(terminal.id);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    }

    /**
     * Get HTML for webview
     */
    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get URIs for resources
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.css'));

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>Custom Terminal</title>
            </head>
            <body>
                <div id="app">
                    <div id="terminal-tabs"></div>
                    <div id="terminal-container"></div>
                    <div id="terminal-controls">
                        <button id="create-terminal-btn">New Terminal</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
