{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,gEAA6D;AAC7D,yEAAsE;AACtE,mDAAgD;AAEhD,IAAI,eAAgC,CAAC;AACrC,IAAI,WAAwB,CAAC;AAC7B,IAAI,YAA8C,CAAC;AAEnD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,8BAA8B;IAC9B,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,CAAC,CAAC;IAE/C,iBAAiB;IACjB,WAAW,GAAG,IAAI,yBAAW,CAAC,eAAe,CAAC,CAAC;IAE/C,oBAAoB;IACpB,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QACtG,IAAI;YACA,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAE3C,gCAAgC;YAChC,IAAI,YAAY,EAAE;gBACd,YAAY,CAAC,MAAM,EAAE,CAAC;aACzB;iBAAM;gBACH,YAAY,GAAG,IAAI,2CAAoB,CAAC,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBAC/E,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE;oBAC3B,YAAY,GAAG,SAAS,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACN;YAED,wBAAwB;YACxB,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,mBAAmB,EAAE,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEtD,iCAAiC;YACjC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;YAC9E,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAElD,kCAAkC;IAClC,OAAO,WAAW,CAAC;AACvB,CAAC;AA3CD,4BA2CC;AAED,SAAgB,UAAU;IACtB,IAAI,eAAe,EAAE;QACjB,eAAe,CAAC,OAAO,EAAE,CAAC;KAC7B;AACL,CAAC;AAJD,gCAIC"}