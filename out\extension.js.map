{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,gEAA6D;AAC7D,+EAA4E;AAC5E,mDAAgD;AAEhD,IAAI,eAAgC,CAAC;AACrC,IAAI,WAAwB,CAAC;AAE7B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,8BAA8B;IAC9B,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,CAAC,CAAC;IAE/C,iBAAiB;IACjB,WAAW,GAAG,IAAI,yBAAW,CAAC,eAAe,CAAC,CAAC;IAE/C,4BAA4B;IAC5B,MAAM,eAAe,GAAG,IAAI,iDAAuB,CAAC,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAC3F,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,6BAA6B,EAAE,eAAe,CAAC,CAC5F,CAAC;IAEF,oBAAoB;IACpB,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QACtG,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,mBAAmB,EAAE,CAAC;YAC7D,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;SACjF;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAC5G,IAAI;YACA,uCAAuC;YACvC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC1C,MAAM,EAAE,sCAAsC;gBAC9C,WAAW,EAAE,eAAe;aAC/B,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE;gBACP,OAAO;aACV;YAED,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC1C,MAAM,EAAE,8BAA8B;gBACtC,WAAW,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,iBAAiB,CAAC;gBACrD,IAAI;gBACJ,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;aACnC,CAAC,CAAC;YAEH,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;SAC9E;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC7E;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,CAAC;IAE5E,kCAAkC;IAClC,OAAO,WAAW,CAAC;AACvB,CAAC;AA3DD,4BA2DC;AAED,SAAgB,UAAU;IACtB,IAAI,eAAe,EAAE;QACjB,eAAe,CAAC,OAAO,EAAE,CAAC;KAC7B;AACL,CAAC;AAJD,gCAIC"}