{"version": 3, "file": "CustomTerminalProvider.js", "sourceRoot": "", "sources": ["../../src/terminal/CustomTerminalProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AAWzB,MAAa,sBAAsB;IAK/B,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAJ5C,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QACpD,oBAAe,GAAG,CAAC,CAAC;QACpB,gBAAW,GAAwB,EAAE,CAAC;QAG1C,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CACL,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAA+B;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,MAAM,YAAY,GAAG,OAAO,EAAE,IAAI,IAAI,mBAAmB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEhF,kCAAkC;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC;QAC1D,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC;QAE1D,gCAAgC;QAChC,MAAM,GAAG,GAAG;YACR,GAAG,OAAO,CAAC,GAAG;YACd,GAAG,OAAO,EAAE,GAAG;YACf,oDAAoD;YACpD,eAAe,EAAE,MAAM;YACvB,kBAAkB,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;SAC1F,CAAC;QAEF,0BAA0B;QAC1B,MAAM,eAAe,GAA2B;YAC5C,IAAI,EAAE,YAAY;YAClB,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,SAAS;YACpB,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,OAAO,EAAE;YACvF,GAAG,EAAE,GAAG;YACR,QAAQ,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC;SACpD,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,eAAe,CAAC,CAAC;QAEhE,sBAAsB;QACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE/D,2BAA2B;QAC3B,MAAM,UAAU,GAAG,GAAG,CAAC,kBAAkB,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEzC,oBAAoB;QACpB,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7B,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,SAAS,UAAU,GAAG,CAAC,CAAC;QAE5E,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,OAA+B;QACjE,MAAM,UAAU,GAA0B;YACtC,GAAG,OAAO;YACV,IAAI,EAAE,QAAQ,IAAI,EAAE;YACpB,GAAG,EAAE;gBACD,GAAG,OAAO,EAAE,GAAG;gBACf,QAAQ,EAAE,IAAI;aACjB;SACJ,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEvD,mBAAmB;QACnB,UAAU,CAAC,GAAG,EAAE;YACZ,QAAQ,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QACrC,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,UAAkB,EAAE,IAAY,EAAE,aAAsB,IAAI;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,UAAkB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,QAAQ,CAAC,IAAI,EAAE,CAAC;SACnB;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,UAAkB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,QAAQ,CAAC,IAAI,EAAE,CAAC;SACnB;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAkB;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACrC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,wBAAwB;QACxB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YAC5C,QAAQ,CAAC,OAAO,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,0BAA0B;QAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,eAAe;QACnB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAE/B,oCAAoC;QACpC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;QAExE,IAAI,SAAiB,CAAC;QACtB,IAAI,SAAS,GAAa,EAAE,CAAC;QAE7B,IAAI,QAAQ,KAAK,OAAO,EAAE;YACtB,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC;gBAC5B,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC;gBACpC,gBAAgB,CAAC;YAC5B,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;SACrD;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;YAC9B,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAChC,UAAU,CAAC;YACtB,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;SACjD;aAAM;YACH,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBAClC,WAAW,CAAC;YACvB,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;SACnD;QAED,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAyB;QAC3C,gEAAgE;QAChE,UAAU,CAAC,GAAG,EAAE;YACZ,yBAAyB;YACzB,QAAQ,CAAC,QAAQ,CAAC,0EAA0E,CAAC,CAAC;YAE9F,uCAAuC;YACvC,+DAA+D;QACnE,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAyB;QAC9C,4CAA4C;QAC5C,KAAK,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;YACzD,IAAI,cAAc,KAAK,QAAQ,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,CAAC,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC1B,MAAM;aACT;SACJ;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAyB;QAC9C,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,OAAe;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;IAC5G,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE,SAAoB,EAAE,OAA+B;QAClG,OAAO,IAAI,CAAC,cAAc,CAAC;YACvB,GAAG,OAAO;YACV,SAAS;YACT,SAAS;SACZ,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,OAA+B;QAC1D,OAAO,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,EAAE,EAAE;YACtD,GAAG,OAAO;YACV,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,YAAY;SACtC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAA+B;QACnD,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,EAAE,EAAE;YAC/C,GAAG,OAAO;YACV,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,gBAAgB;SAC1C,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAA+B;QACpD,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;QACtE,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,EAAE,EAAE;YAC9C,GAAG,OAAO;YACV,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,MAAM;SAChC,CAAC,CAAC;IACP,CAAC;CACJ;AApSD,wDAoSC"}