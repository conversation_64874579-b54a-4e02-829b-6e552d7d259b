{"version": 3, "file": "Terminal.js", "sourceRoot": "", "sources": ["../../src/terminal/Terminal.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAStC,IAAY,YAKX;AALD,WAAY,YAAY;IACpB,+BAAe,CAAA;IACf,2BAAW,CAAA;IACX,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;AACrB,CAAC,EALW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAKvB;AAED,IAAY,cAMX;AAND,WAAY,cAAc;IACtB,+CAA6B,CAAA;IAC7B,yCAAuB,CAAA;IACvB,+CAA6B,CAAA;IAC7B,iCAAe,CAAA;IACf,uCAAqB,CAAA;AACzB,CAAC,EANW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAMzB;AAYD,MAAsB,QAAS,SAAQ,qBAAY;IAW/C,YAAY,EAAU,EAAE,IAAkB,EAAE,UAA2B,EAAE;QACrE,KAAK,EAAE,CAAC;QARF,WAAM,GAAmB,cAAc,CAAC,YAAY,CAAC;QACrD,iBAAY,GAAW,EAAE,CAAC;QAC1B,mBAAc,GAAa,EAAE,CAAC;QAE9B,eAAU,GAAuB,EAAE,CAAC;QAK1C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAOD;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE,EAAE;YAC1F,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5C;QAED,kCAAkC;QAClC,MAAM,SAAS,GAAqB;YAChC,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC9B,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,2BAA2B;QAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC9D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,aAAa;QACT,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAY,EAAE,IAAY;QAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;SAC1B;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC;QAEtC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC/B;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YACpC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;YAE1B,kCAAkC;YAClC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,IAAI,CAAC;aACxC;YAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAgB,EAAE,EAAE;YACxC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC;YAE1C,6BAA6B;YAC7B,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC3C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC1C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;aACrC;YAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,MAAsB;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACO,mBAAmB;QACzB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,WAAmB,KAAK;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzD;IACL,CAAC;CACJ;AAtMD,4BAsMC"}