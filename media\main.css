/* Custom Terminal Styles */

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    height: 100vh;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Terminal Tabs */
#terminal-tabs {
    display: flex;
    background-color: var(--vscode-tab-inactiveBackground);
    border-bottom: 1px solid var(--vscode-tab-border);
    min-height: 35px;
    overflow-x: auto;
    flex-shrink: 0;
}

.terminal-tab {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--vscode-tab-inactiveBackground);
    color: var(--vscode-tab-inactiveForeground);
    border-right: 1px solid var(--vscode-tab-border);
    cursor: pointer;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    user-select: none;
}

.terminal-tab:hover {
    background-color: var(--vscode-tab-hoverBackground);
    color: var(--vscode-tab-hoverForeground);
}

.terminal-tab.active {
    background-color: var(--vscode-tab-activeBackground);
    color: var(--vscode-tab-activeForeground);
    border-bottom: 2px solid var(--vscode-tab-activeBorder);
}

.terminal-tab span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
}

.close-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 16px;
    margin-left: 8px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.close-btn:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

/* Terminal Container */
#terminal-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: var(--vscode-terminal-background, #1e1e1e);
}

.terminal-instance {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.terminal-instance .xterm {
    height: 100%;
    width: 100%;
}

.terminal-instance .xterm-viewport {
    background-color: transparent !important;
}

.terminal-instance .xterm-screen {
    background-color: transparent !important;
}

/* Terminal Controls */
#terminal-controls {
    display: flex;
    gap: 8px;
    padding: 8px;
    background-color: var(--vscode-panel-background);
    border-top: 1px solid var(--vscode-panel-border);
    flex-shrink: 0;
}

#terminal-controls button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 12px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    font-family: inherit;
}

#terminal-controls button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

#terminal-controls button:active {
    background-color: var(--vscode-button-activeBackground);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--vscode-descriptionForeground);
    text-align: center;
    padding: 20px;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: normal;
}

.empty-state p {
    margin: 0 0 20px 0;
    font-size: 14px;
    line-height: 1.4;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-connected {
    background-color: #4caf50;
}

.status-connecting {
    background-color: #ff9800;
    animation: pulse 1.5s infinite;
}

.status-disconnected {
    background-color: #f44336;
}

.status-error {
    background-color: #e91e63;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 600px) {
    .terminal-tab {
        min-width: 100px;
        max-width: 150px;
    }
    
    .terminal-tab span {
        font-size: 12px;
    }
    
    #terminal-controls {
        flex-wrap: wrap;
    }
    
    #terminal-controls button {
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Focus Styles */
.terminal-tab:focus,
#terminal-controls button:focus {
    outline: 1px solid var(--vscode-focusBorder);
    outline-offset: -1px;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid var(--vscode-progressBar-background);
    border-radius: 50%;
    border-top-color: var(--vscode-progressBar-foreground);
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .terminal-tab {
        border: 1px solid;
    }
    
    .terminal-tab.active {
        border-width: 2px;
    }
    
    #terminal-controls button {
        border: 1px solid;
    }
}
