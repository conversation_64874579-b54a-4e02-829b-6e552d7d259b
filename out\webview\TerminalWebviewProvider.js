"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalWebviewProvider = void 0;
const vscode = __importStar(require("vscode"));
class TerminalWebviewProvider {
    constructor(_extensionUri, _terminalManager) {
        this._extensionUri = _extensionUri;
        this._terminalManager = _terminalManager;
        this.setupTerminalManagerEvents();
    }
    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(message => {
            this.handleWebviewMessage(message);
        }, undefined, []);
        // Send initial state
        this.sendTerminalList();
    }
    /**
     * Show specific terminal
     */
    showTerminal(terminalId) {
        this._currentTerminalId = terminalId;
        if (this._view) {
            this._view.webview.postMessage({
                type: 'showTerminal',
                terminalId: terminalId
            });
        }
    }
    /**
     * Handle messages from webview
     */
    handleWebviewMessage(message) {
        switch (message.type) {
            case 'ready':
                this.sendTerminalList();
                break;
            case 'selectTerminal':
                this.selectTerminal(message.terminalId);
                break;
            case 'sendInput':
                this.sendInputToTerminal(message.terminalId, message.input);
                break;
            case 'executeCommand':
                this.executeCommand(message.terminalId, message.command);
                break;
            case 'resizeTerminal':
                this.resizeTerminal(message.terminalId, message.cols, message.rows);
                break;
            case 'clearTerminal':
                this.clearTerminal(message.terminalId);
                break;
            case 'closeTerminal':
                this.closeTerminal(message.terminalId);
                break;
            case 'createLocalTerminal':
                this.createLocalTerminal();
                break;
            case 'createSSHTerminal':
                this.createSSHTerminal(message.config);
                break;
        }
    }
    /**
     * Setup terminal manager event handlers
     */
    setupTerminalManagerEvents() {
        this._terminalManager.on('terminalCreated', (terminal) => {
            this.sendTerminalList();
            this.setupTerminalEvents(terminal);
        });
        this._terminalManager.on('terminalClosed', (terminalId) => {
            this.sendTerminalList();
            if (this._currentTerminalId === terminalId) {
                this._currentTerminalId = undefined;
            }
        });
        this._terminalManager.on('terminalData', (terminalId, data) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'terminalData',
                    terminalId: terminalId,
                    data: data
                });
            }
        });
    }
    /**
     * Setup events for a specific terminal
     */
    setupTerminalEvents(terminal) {
        terminal.on('statusChanged', (status) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'terminalStatusChanged',
                    terminalId: terminal.id,
                    status: status
                });
            }
        });
        terminal.on('commandExecuted', (execution) => {
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'commandExecuted',
                    terminalId: terminal.id,
                    execution: execution
                });
            }
        });
    }
    /**
     * Send terminal list to webview
     */
    sendTerminalList() {
        if (this._view) {
            const terminals = this._terminalManager.getAllTerminals().map(terminal => ({
                id: terminal.id,
                type: terminal.type,
                status: terminal.getStatus()
            }));
            this._view.webview.postMessage({
                type: 'terminalList',
                terminals: terminals
            });
        }
    }
    /**
     * Select terminal
     */
    selectTerminal(terminalId) {
        this._currentTerminalId = terminalId;
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal && this._view) {
            this._view.webview.postMessage({
                type: 'terminalSelected',
                terminalId: terminalId,
                output: terminal.getOutput(),
                history: terminal.getCommandHistory()
            });
        }
    }
    /**
     * Send input to terminal
     */
    async sendInputToTerminal(terminalId, input) {
        try {
            await this._terminalManager.sendInput(terminalId, input);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to send input: ${error}`);
        }
    }
    /**
     * Execute command in terminal
     */
    async executeCommand(terminalId, command) {
        try {
            await this._terminalManager.executeCommand(terminalId, command);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to execute command: ${error}`);
        }
    }
    /**
     * Resize terminal
     */
    resizeTerminal(terminalId, cols, rows) {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.resize(cols, rows);
        }
    }
    /**
     * Clear terminal
     */
    clearTerminal(terminalId) {
        const terminal = this._terminalManager.getTerminal(terminalId);
        if (terminal) {
            terminal.clear();
        }
    }
    /**
     * Close terminal
     */
    async closeTerminal(terminalId) {
        try {
            await this._terminalManager.closeTerminal(terminalId);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to close terminal: ${error}`);
        }
    }
    /**
     * Create local terminal
     */
    async createLocalTerminal() {
        try {
            const terminal = await this._terminalManager.createLocalTerminal();
            this.showTerminal(terminal.id);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create terminal: ${error}`);
        }
    }
    /**
     * Create SSH terminal
     */
    async createSSHTerminal(config) {
        try {
            const terminal = await this._terminalManager.createSSHTerminal(config);
            this.showTerminal(terminal.id);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create SSH terminal: ${error}`);
        }
    }
    /**
     * Get HTML for webview
     */
    _getHtmlForWebview(webview) {
        // Get URIs for resources
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>Custom Terminal</title>
            </head>
            <body>
                <div id="app">
                    <div id="terminal-tabs"></div>
                    <div id="terminal-container"></div>
                    <div id="terminal-controls">
                        <button id="create-local-btn">New Local Terminal</button>
                        <button id="create-ssh-btn">New SSH Terminal</button>
                    </div>
                </div>
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
exports.TerminalWebviewProvider = TerminalWebviewProvider;
TerminalWebviewProvider.viewType = 'customTerminal.terminalView';
//# sourceMappingURL=TerminalWebviewProvider.js.map